{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net (onnx_dereverb): É a melhor opção para reverberação de dois canais, mas não pode remover a reverberação de um único canal;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:Remove os efeitos de atraso. Aggressive é mais completo que Normal na remoção, DeReverb remove adicionalmente a reverberação, pode remover a reverberação de um canal único, mas não remove completamente a reverberação de placa de alta frequência.", "*实验/模型名": "*Nome do experimento/modelo", "*文本标注文件": "*Arquivo de marcação de texto", "*训练集音频文件目录": "*Diretório de arquivos de áudio do conjunto de treinamento", "*请上传并填写参考信息": "Por favor, faça o upload e preencha as informações de referência", "*请填写需要合成的目标文本和语种模式": "*Por favor, insira o texto alvo a ser sintetizado e o modo de idioma.", ".限制范围越小判别效果越好。": "Menos multilinguismo é me<PERSON>hor", "1-GPT-SoVITS-TTS": "1-GPT-SOVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. O tempo de processamento do modelo DeEcho-DeReverb é quase o dobro dos outros dois modelos DeEcho;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Manter a voz: selecione isso para áudio sem harmonia, que preserva melhor a voz principal do que o HP5. Inclui dois modelos, HP2 e HP3; o HP3 pode permitir um pequeno vazamento de acompanhamento, mas preserva a voz principal um pouco melhor do que o HP2;", "2-GPT-SoVITS-变声": "2-gpt-sovits-mudança de voz", "2、MDX-Net-Dereverb模型挺慢的；": "2. O modelo MDX-Net-Dereverb é bastante lento;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. <PERSON><PERSON> apenas a voz principal: selecione isso para áudio com harmonia, pode haver uma redução na voz principal. Inclui um modelo HP5;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. A configuração mais limpa recomendada é usar primeiro o MDX-Net e depois o DeEcho-Aggressive.", "3、去混响、去延迟模型（by FoxJoy）：": "3. Modelo de remoção de reverberação e atraso (por FoxJoy):", "ASR 模型": "Modelo ASR", "ASR 模型尺寸": "Tamanho do modelo ASR", "ASR 语言设置": "Configurações de idioma do ASR", "CPU训练,较慢": "Treinamento em CPU, mais lento", "GPT 训练: 模型权重文件在 GPT_weights/": "Treinamento GPT: O arquivo de pesos do modelo está em GPT_weights/", "GPT模型列表": "Lista de modelos GPT", "GPT训练": "Treinamento GPT", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "Parâmetros de amostragem do GPT (não muito baixos quando não houver texto de referência. Use o padrão se não tiver certeza):", "GPU卡号,只能填1个整数": "Número da placa de vídeo, só é possível preencher com um número inteiro", "GPU卡号以-分割，每个卡号一个进程": "Número da placa de vídeo dividido por-, cada número de placa é um processo", "LoRA秩": "Classificação LoRA", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "Treinamento SoVITS: O arquivo de pesos do modelo está em SoVITS_weights/", "SoVITS模型列表": "Lista de modelos SoVITS", "SoVITS训练": "Treinamento SoVITS", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "Enviar texto: Salve manualmente o conteúdo de todos os campos de texto da página atual na memória e no arquivo. Se você não clicar neste botão antes ou depois de mudar de página ou sair da tela de anotação, ao retornar as alterações serão desfeitas e todo o trabalho será perdido.", "TTS推理WebUI": "Inferência TTS WebUI", "UVR5人声伴奏分离&去混响去延迟工具": "UVR5 Separação de voz e acompanhamento & remoção de reverberação e atraso", "V3不支持无参考文本模式，请填写参考文本！": "O modo sem texto de referência não é suportado pelo V3. Por favor, forneça um texto de referência!", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: Em que proporção o áudio normalizado é misturado de volta", "batch_size": "Tamanho do Lote", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "HOP_SIZE: Como calcular a curva de volume, quanto menor a precisão, maior a quantidade de cálculos (não significa que quanto maior a precisão, melhor o efeito)", "max:归一化后最大值多少": "MAX: Qual é o valor máximo após a normalização?", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: <PERSON><PERSON><PERSON> de cortar, por quanto tempo no máximo o silêncio é mantido", "min_interval:最短切割间隔": "min_interval: O intervalo de corte mínimo", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: Comprimento mínimo de cada segmento. Se o primeiro segmento for muito curto, ele será unido aos segmentos seguintes até exceder este valor", "temperature": "temperatura", "threshold:音量小于这个值视作静音的备选切割点": "Limiar: O volume menor que este valor é considerado como um ponto de corte mudo alternativo", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "O v3 não suporta este modo no momento. O uso resultará em erro.", "v3输出如果觉得闷可以试试开超分": "Se a saída do v3 parecer aba<PERSON>da, tente ativar a super-resolução", "不切": "Não dividir", "不训练直接推v2ProPlus底模！": "Usar diretamente o modelo base v2ProPlus sem treinamento!", "不训练直接推v2Pro底模！": "Usar diretamente o modelo base v2Pro sem treinamento!", "不训练直接推v2底模！": "Usar diretamente o modelo base v2 sem treinamento!", "不训练直接推v3底模！": "Usar diretamente o modelo base v3 sem treinamento!", "中文": "<PERSON><PERSON><PERSON>", "中英混合": "Mistura de Chinês e Inglês", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "<PERSON><PERSON><PERSON> de Referência Principal (Por favor, carregue um áudio de referência entre 3-10 segundos, exceder esse limite causará um erro!)", "主参考音频的文本": "Texto do Áudio de Referência Principal", "主参考音频的语种": "Idioma do Áudio de Referência Principal", "也可批量输入音频文件, 二选一, 优先读文件夹": "Também é possível inserir arquivos de áudio em lote; escolha uma opção, preferencialmente leia a pasta.", "人声伴奏分离批量处理， 使用UVR5模型。": "Processamento em lote de separação de voz e acompanhamento, usando o modelo UVR5.", "人声分离WebUI": "WebUI de separação de voz", "人声提取激进程度": "Grau de agressividade da extração de voz", "以下文件或文件夹不存在": "Nenhum Arquivo ou Pasta Encontrado", "以下模型不存在:": "<PERSON>en<PERSON>o <PERSON>:", "伴奏人声分离&去混响&去回声": "Separação de acompanhamento e voz & remoção de reverberação & remoção de eco", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "Os parâmetros do modelo de super-resolução não foram baixados, então a super-resolução não será realizada. Para utilizá-la, siga o tutorial e baixe os arquivos necessários.", "使用无参考文本模式时建议使用微调的GPT": "Recomenda-se o uso de um modelo GPT ajustado ao utilizar o modo sem texto de referência.", "保存频率save_every_epoch": "Frequência de salvamento save_every_epoch", "保持随机": "<PERSON><PERSON>", "关闭": "<PERSON><PERSON><PERSON> ", "凑50字一切": "Complete com 50 caracteres", "凑四句一切": "Complete com quatro frases", "分桶处理模式已关闭": "Modo de Processamento em Balde Desativado", "分桶处理模式已开启": "Modo de Processamento em Balde Ativado", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "Modo de Retorno Segmentado não suporta Processamento em Balde, Processamento em Balde Desativado Automaticamente", "分段返回模式已开启": "Modo de Retorno Segmentado Ativado", "分段间隔(秒)": "Intervalo de Segmentação (Segundos)", "分段间隔过小，已自动设置为0.01": "Intervalo de Segmentação muito curto, automaticamente definido como 0.01", "切分": "Segmentação", "切分后文本": "Texto de Inferência Após Segmentação", "切分后的子音频的输出根目录": "Diretório raiz de saída do sub-áudio após o corte", "切分文本": "Segmentar Texto", "切割使用的进程数": "Número de processos para corte", "刷新模型路径": "Atualizar caminho do modelo", "前端处理后的文本(每句):": "Texto após processamento front-end (por frase):", "前置数据集获取工具": "Ferramenta de obtenção de conjunto de dados pré-processado", "占用中": " Ocupado", "去混响/去延迟，附：": "Remoção de reverberação/remoção de atraso, anexo:", "参考音频在3~10秒范围外，请更换！": "O áudio de referência está fora do intervalo de 3 a 10 segundos. Por favor, substitua!", "参考音频的文本": "Texto do áudio de referência", "参考音频的语种": "Idioma do áudio de referência", "句间停顿秒数": "Tempo de pausa entre frases (segundos)", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Opcional: Faça upload de vários arquivos de áudio de referência arrastando e soltando-os (recomendado que sejam do mesmo gênero) e faça uma média dos seus tons. Se essa opção for deixada em branco, o tom será controlado pelo único áudio de referência à esquerda. Se estiver ajustando o modelo, é recomendado que todos os arquivos de áudio de referência tenham tons dentro do conjunto de treinamento de ajuste; o modelo pré-treinado pode ser ignorado.", "合成语音": "<PERSON><PERSON>", "合成音频": "<PERSON><PERSON><PERSON><PERSON>", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Exemplo de formato de caminho de pasta válido: E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (copie do endereço da barra do gerenciador de arquivos).", "后续将支持转音素、手工修改音素、语音合成分步执行。": "Suporte para Conversão de Fonemas, Edição Manual de Fonemas e Síntese de Fase por Fase será adicionado no futuro.", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "Se não conseguir entender claramente o áudio de referência, ative esta opção. Quando ativada, o texto de referência inserido será ignorado.", "启用并行推理版本": "Ativar Versão de Inferência Paralela", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Preencha o diretório onde os áudios cortados estão localizados! O caminho completo dos arquivos de áudio lidos = este diretório - concatenação com o nome do arquivo de forma correspondente no arquivo .list (não o caminho completo). Se deixar em branco, use o caminho absoluto no arquivo .list.", "多语种混合": "Mistura de múltiplos idiomas", "多语种混合(粤语)": "Mistura Multilíngue (Yue)", "失败": " <PERSON><PERSON><PERSON>", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "Se você não concorda com estes termos, não poderá usar ou referenciar nenhum código ou arquivo deste pacote de software. Consulte o arquivo LICENSE no diretório raiz para mais detalhes.", "实际输入的参考文本:": "Texto de referência realmente inserido:", "实际输入的目标文本(切句后):": "Texto alvo realmente inserido (após divisão de frases):", "实际输入的目标文本(每句):": "Texto alvo realmente inserido (por frase):", "实际输入的目标文本:": "Texto alvo realmente inserido:", "导出文件格式": "Formato de arquivo de exportação", "已关闭": " <PERSON><PERSON><PERSON>", "已完成": " <PERSON><PERSON><PERSON><PERSON><PERSON>", "已开启": " <PERSON><PERSON>do", "并行合成中": "Síntese em paralelo em andamento", "并行推理": "Inferência Paralela", "并行推理模式已关闭": "Modo de Inferência Paralela Desativado", "并行推理模式已开启": "Modo de Inferência Paralela Ativado", "底模缺失，无法加载相应 LoRA 权重": "Falta o modelo base, não foi possível carregar os pesos LoRA correspondentes", "开启": "Ativar ", "开启无参考文本模式。不填参考文本亦相当于开启。": "Ativar o modo sem texto de referência. Não preencher o texto de referência também equivale a ativar.", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "Quando o modo de inferência paralela está ativado, os modelos SoVITS V3/4 não suportam processamento por lotes e esta funcionalidade foi automaticamente desativada.", "微调模型信息": "Informações do modelo ajustado", "微调训练": "Treinamento de ajuste fino", "怎么切": "Como cortar", "总训练轮数total_epoch": "Total de epoch de treinamento", "总训练轮数total_epoch，不建议太高": "Total de epoch de treinamento, não é recomendável um valor muito alto", "指定输出主人声文件夹": "Especificar a pasta de saída da voz principal", "指定输出非主人声文件夹": "Especificar a pasta de saída da voz secundária", "按中文句号。切": "Dividir por ponto final chinês", "按标点符号切": "Dividir por sinais de pontuação", "按英文句号.切": "Dividir por ponto final em inglês", "推理": "Inferência", "推理设置": "Configurações de Inferência", "提取文本Bert特征": "Extrair Características BERT do Texto", "数据分桶(并行推理时会降低一点计算量)": "Agrupamento de Dados (Reduz o Custo Computacional na Inferência Paralela)", "数据类型精度": "precisão do tipo de dado", "文本分词与特征提取": "Tokenização de texto e extração de características", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "Ferramenta de Segmentação de Texto. Textos muito longos podem não resultar em uma boa síntese, então é recomendado segmentar. A síntese será feita com base nas quebras de linha e depois concatenada.", "文本模块学习率权重": "Weight da taxa de aprendizado do módulo de texto", "施工中，请静候佳音": "Em construção, por favor, aguarde por um bom som", "日文": "<PERSON><PERSON><PERSON><PERSON>", "日英混合": "Mistura de Japonês e Inglês", "是否仅保存最新的权重文件以节省硬盘空间": "Deseja salvar apenas os arquivos de pesos mais recentes para economizar espaço em disco?", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Se deve salvar o modelo pequeno final na pasta Weights em cada ponto de salvamento de tempo", "是否开启DPO训练选项(实验性)": "Ativar a opção de treinamento DPO (experimental)?", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Ajuste a velocidade da fala e o tom do último resultado de síntese para evitar aleatoriedade.", "显卡信息": "Informações da placa de vídeo", "未下载模型": "<PERSON>o não baixado", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "Este software é de código aberto sob a licença MIT, e o autor não tem controle sobre seu uso. O usuário e qualquer pessoa que distribua áudio gerado pelo software são totalmente responsáveis.", "标注文件路径 (含文件后缀 *.list)": "Caminho do arquivo de anotações (com a extensão *.list)", "模型": "<PERSON><PERSON>", "模型分为三类：": "Modelos dividem-se em três categorias:", "模型切换": "Troca de modelo", "模型加载中，请等待": "Carregando o modelo, por favor aguarde...", "每张显卡的batch_size": "Tamanho do lote de cada placa de vídeo", "粤英混合": "<PERSON><PERSON><PERSON><PERSON>", "粤语": "<PERSON><PERSON>", "终止合成": "<PERSON><PERSON><PERSON><PERSON>", "缺少Hubert数据集": "Conjunto de Dados <PERSON>", "缺少语义数据集": "Conjunto de Dados Semânticos Ausente", "缺少音素数据集": "Conjunto de Dados de Fonemas Ausente", "缺少音频数据集": "Conjunto de Dados de Áudio Ausente", "英文": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "训练模型的版本": "Versão do modelo treinado", "训练集格式化一键三连": "Formatação do conjunto de treinamento em um clique", "训练集格式化工具": "Ferramenta de formatação do conjunto de treinamento", "语义Token提取": "Extração de tokens semânticos", "语速": "Velocidade da fala", "语速调整，高为更快": "Ajustar a velocidade da fala, mais alta para mais rápido", "语速调节不支持分桶处理，已自动关闭分桶处理": "Ajuste de Velocidade de Fala não suporta Processamento em Balde, Processamento em Balde Desativado Automaticamente", "语音切分": "Segmentação de áudio", "语音切分工具": "Ferramenta de segmentação de áudio", "语音文本校对标注工具": "Ferramenta de anotação e revisão de texto de áudio", "语音自监督特征提取": "Extração de características de autoaprendizagem de áudio", "语音识别": "Reconhecimento de voz", "语音识别工具": "Ferramenta de reconhecimento de voz", "语音降噪": "Redução de ruído de áudio", "请上传3~10秒内参考音频，超过会报错！": "Por favor, faça upload de um áudio de referência com duração entre 3 e 10 segundos. Áudios fora dessa faixa causarão erro!", "请上传参考音频": "<PERSON><PERSON> <PERSON>, Carregue o Áudio de Referência", "请填入推理文本": "<PERSON><PERSON>, Preencha o Texto de Inferência", "请填入正确的List路径": "<PERSON><PERSON>, Insira o Caminho Correto da Lista", "请填入正确的音频文件夹路径": "<PERSON><PERSON>, Insira o Caminho Correto da Pasta de Áudio", "请输入有效文本": "Por favor, insira um texto válido", "路径不存在,使用默认配置": "Caminhos Não Encontrados, Usando Configuração Padrão", "路径不能为空": "<PERSON><PERSON><PERSON> Caminho Não Vazio", "路径错误": "<PERSON><PERSON>", "转换": "Converter", "辅参考音频(可选多个，或不选)": "Áudio de Referência Secundário (Múltiplos Opcionais ou Nenhum)", "输入待处理音频文件夹路径": "Caminho da pasta de arquivos de áudio a ser processados", "输入文件夹路径": "<PERSON><PERSON><PERSON> da pasta de entrada", "输入路径不存在": "O caminho de entrada não existe", "输入路径存在但不可用": "O caminho de entrada existe, mas não está disponível", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Logs de saída/deve haver arquivos e pastas começando com 23456 no diretório do nome do experimento", "输出信息": "Informações de saída", "输出文件夹路径": "<PERSON><PERSON><PERSON> da pasta de saída", "输出的语音": "Áudio de saída", "运行中": " Em execução", "进度": "Progresso", "进程已终止": " Processo encerrado", "进程输出信息": " Informações de saída do processo", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "Selecione os modelos treinados armazenados em SoVITS_weights e GPT_weights. Os padrões são modelos base para experimentar inferência Zero Shot TTS de 5 segundos sem treinamento.", "采样步数(仅对V3/4生效)": "Número de passos de amostragem (apenas válido para V3/4)", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "Passos de Amostragem: Se parecer ruidoso, tente aumentar; se parecer lento, tente diminuir", "重复惩罚": "Penalidade de Repetição", "随机种子": "Semente Aleatória", "需先终止才能开启下一次任务": "É necessário encerrar primeiro para iniciar a próxima tarefa", "需要合成的切分前文本": "Texto de Inferência Antes da Segmentação", "需要合成的文本": "Texto a ser sintetizado", "需要合成的文本的语种": "Idioma do Texto de Inferência", "需要合成的语种": "Idioma a ser sintetizado", "韩文": "<PERSON><PERSON>", "韩英混合": "Mistura <PERSON>ano-Inglês", "音频加载失败": "Falha ao Carregar o Áudio", "音频文件不存在，跳过：": "Arquivo de áudio não encontrado, pulando: ", "音频标注WebUI": "WebUI de Anotação de Áudio", "音频自动切分输入路径，可文件可文件夹": "Caminho de entrada para divisão automática de áudio (arquivo ou pasta)", "音频超分中": "Executando Super-Resolução de Áudio", "音频超采样": "Superamostragem de áudio", "音频超采样(仅对V3生效))": "Superamostragem de áudio (apenas válida para V3)", "预测语义Token": "Prever token semântico", "预训练GPT模型路径": "Caminho do modelo GPT pré-treinado", "预训练SSL模型路径": "Caminho do modelo SSL pré-treinado", "预训练SoVITS-D模型路径": "Caminho do modelo SoVITS-D pré-treinado", "预训练SoVITS-G模型路径": "Caminho do modelo SoVITS-G pré-treinado", "预训练中文BERT模型路径": "Caminho do modelo BERT chinês pré-treinado", "预训练模型路径": "Caminho do modelo pré-treinado"}