{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb):二重チャンネルのリバーブに最適な選択ですが、単一チャンネルのリバーブは除去できません；", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:遅延効果を除去します。AggressiveはNormalよりも徹底的に除去し、DeReverbは追加でリバーブを除去し、モノラルリバーブを除去できますが、高周波数のプレートリバーブは完全には除去できません。", "*实验/模型名": "*実験/モデル名", "*文本标注文件": "*テキスト注釈ファイル", "*训练集音频文件目录": "*トレーニングデータのオーディオファイルディレクトリ", "*请上传并填写参考信息": "*参照情報をアップロードして記入してください", "*请填写需要合成的目标文本和语种模式": "*合成対象テキストと言語モードを入力してください", ".限制范围越小判别效果越好。": "多言語対応を減らした方が良い", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1、DeEcho-DeReverbモデルの処理時間は、他の2つのDeEchoモデルのほぼ2倍です；", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1、主音を保持: ハーモニーなしの音声にはこのオプションを選択し、HP5よりも主音の保持が優れています。HP2とHP3の2つのモデルが内蔵されており、HP3はわずかに伴奏を漏らす可能性がありますが、HP2よりも主音の保持がわずかに良いです；", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-ボイスチェンジャー", "2、MDX-Net-Dereverb模型挺慢的；": "2、MDX-Net-Dereverbモデルはかなり遅いです；", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2、主音のみを保持: ハーモニー付きの音声にはこのオプションを選択し、主音が弱くなる可能性があります。HP5モデルが1つ内蔵されています；", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3、最もクリーンな設定は、MDX-Netの後にDeEcho-Aggressiveを使用することをお勧めします。", "3、去混响、去延迟模型（by FoxJoy）：": "3、リバーブ除去と遅延除去モデル（by FoxJoy）：", "ASR 模型": "ASR モデル", "ASR 模型尺寸": "ASRモデルサイズ", "ASR 语言设置": "ASR 言語設定", "CPU训练,较慢": "CPUトレーニング、速度低下", "GPT 训练: 模型权重文件在 GPT_weights/": "GPT トレーニング: モデルの重みファイルは GPT_weights/ にあります", "GPT模型列表": "GPTモデルリスト", "GPT训练": "GPTトレーニング", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "GPT サンプリングパラメーター（参照テキストがない場合はあまり低くしないでください。わからない場合はデフォルトを使用してください）：", "GPU卡号,只能填1个整数": "GPU番号、1つの整数しか入力できません", "GPU卡号以-分割，每个卡号一个进程": "GPUカード番号はハイフンで区切り、各カード番号ごとに1つのプロセスが実行されます", "LoRA秩": "LoRAランク", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "SoVITS トレーニング: モデルの重みファイルは SoVITS_weights/ にあります", "SoVITS模型列表": "SoVITSモデルリスト", "SoVITS训练": "SoVITSトレーニング", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "現在のページにあるすべてのテキストフィールドの内容を手動で保存します（メモリとファイルに反映）。ページ切り替えやアノテーション画面の終了前にこのボタンを押さないと、再度戻った際に変更が破棄され、作業が無駄になります。", "TTS推理WebUI": "TTS推論WebUI", "UVR5人声伴奏分离&去混响去延迟工具": "UVR5ボーカルアカンパニメント分離＆リバーブおよびディレイ除去ツール", "V3不支持无参考文本模式，请填写参考文本！": "V3は参照テキストなしのモードをサポートしていません。必ず参照テキストを入力してください！", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix：正規化後のオーディオが入る割合", "batch_size": "バッチサイズ", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: 音量曲線の計算方法、小さいほど精度が高くなりますが、計算量が増加します（精度が高いほど必ずしも効果が良いわけではありません）", "max:归一化后最大值多少": "max：正規化後の最大値", "max_sil_kept:切完后静音最多留多长": "max_sil_kept：切り終えた後、最大でどれだけ静かにするか", "min_interval:最短切割间隔": "min_interval：最短カット間隔", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length：各セグメントの最小長さ。最初のセグメントが短すぎる場合、連続して後続のセグメントに接続され、この値を超えるまで続きます。", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "閾値：この値未満の音量は静音と見なされ、代替のカットポイントとして扱われます", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "v3では現在このモードをサポートしておらず、使用するとエラーが発生します。", "v3输出如果觉得闷可以试试开超分": "v3の出力がこもっていると感じた場合、超解像を試してください", "不切": "切らない", "不训练直接推v2ProPlus底模！": "学習せずに直接v2ProPlusベースモデルを使用！", "不训练直接推v2Pro底模！": "学習せずに直接v2Proベースモデルを使用！", "不训练直接推v2底模！": "学習せずに直接v2ベースモデルを使用！", "不训练直接推v3底模！": "学習せずに直接v3ベースモデルを使用！", "中文": "中国語", "中英混合": "中英混合", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "プライマリーFERエンス音声（3〜10秒以内の参考音声をアップロードしてください。これを超えるとエラーが発生します！）", "主参考音频的文本": "プライマリーFERエンス音声のテキスト", "主参考音频的语种": "プライマリーFERエンス音声の言語", "也可批量输入音频文件, 二选一, 优先读文件夹": "複数のオーディオファイルもインポートできます。フォルダパスが存在する場合、この入力は無視されます。", "人声伴奏分离批量处理， 使用UVR5模型。": "人声と伴奏の分離をバッチ処理で行い、UVR5モデルを使用します。", "人声分离WebUI": "ボーカル分離WebUI", "人声提取激进程度": "人声抽出の積極性", "以下文件或文件夹不存在": "そのようなファイルまたはフォルダは存在しません", "以下模型不存在:": "モデルが存在しません:", "伴奏人声分离&去混响&去回声": "ボーカル/伴奏の分離と残響の除去", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "超解像モデルのパラメータをダウンロードしていないため、超解像は行われません。超解像を行いたい場合は、まずチュートリアルを参照してファイルをダウンロードしてください", "使用无参考文本模式时建议使用微调的GPT": "参照テキストなしモードを使用する際は、微調整されたGPTを推奨めることをお勧びします。", "保存频率save_every_epoch": "保存頻度save_every_epoch", "保持随机": "ランダムを維持", "关闭": "閉じる", "凑50字一切": "50文字ずつカット", "凑四句一切": "4つの文で埋める", "分桶处理模式已关闭": "バケット処理モードを無効化", "分桶处理模式已开启": "バケット処理モードを有効化", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "セグメントされた返却モードはバケット処理をサポートしていないため、自動的にバケット処理を無効化", "分段返回模式已开启": "セグメントされた返却モードを有効化", "分段间隔(秒)": "セグメント間隔（秒）", "分段间隔过小，已自动设置为0.01": "セグメント間隔が短すぎていので、自動的に0.01に設定されました", "切分": "セグメント", "切分后文本": "セグメント後のテキスト", "切分后的子音频的输出根目录": "分割後のサブオーディオの出力ルートディレクトリ", "切分文本": "テキストをセグメント", "切割使用的进程数": "分割に使用されるプロセス数", "刷新模型路径": "モデルのパスを更新", "前端处理后的文本(每句):": "フロントエンド処理後のテキスト（文ごと）:", "前置数据集获取工具": "前処理データセット取得ツール", "占用中": "使用中", "去混响/去延迟，附：": "残響除去/遅延除去、附：", "参考音频在3~10秒范围外，请更换！": "参照音声が3～10秒の範囲外です。別の音声に変更してください！", "参考音频的文本": "参照オーディオのテキスト", "参考音频的语种": "参照オーディオの言語", "句间停顿秒数": "文間のポーズ秒数", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "オプション：複数の参照オーディオファイルをドラッグ＆ドロップしてアップロードし、それらのトーンを平均化します（同性推奨）。このオプションを空白のままにした場合、トーンは左側の単一の参照オーディオによって制御されます。モデルを微調整する場合、すべての参照オーディオファイルが微調整のトレーニングセット内のトーンを持つことをお勧めします。プリトレーニングモデルは無視しても構いません。", "合成语音": "推論を開始", "合成音频": "音声を合成する", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "適切なフォルダパスの例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华テストサンプル（ファイルマネージャのアドレスバーからコピーしてください）。", "后续将支持转音素、手工修改音素、语音合成分步执行。": "今後、フォンメ转换、手動フォンメ編集、音声合成のステップバイステップ実行をサポートします。", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "参照音声がはっきり、または何を書くかわからない場合は、このオプションを有効にして参照テキストを無視します。", "启用并行推理版本": "並列推論バージョンを有効化", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "切断後の音声ファイルが格納されているディレクトリを入力してください！読み取り対象の音声ファイルの完全パス = このディレクトリ - 結合 - listファイル内の波形に対応するファイル名（完全パスではありません）。空白の場合、.listファイル内の絶対完全パスを使用します。", "多语种混合": "多言語混合", "多语种混合(粤语)": "多言語混合(粤語)", "失败": "失敗", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "この条件を認めない場合、ソフトウェアパッケージ内の任意のコードやファイルを使用または引用することはできません。詳細はルートディレクトリのLICENSEを参照してください。", "实际输入的参考文本:": "実際に入力された参照テキスト：", "实际输入的目标文本(切句后):": "実際に入力された目標テキスト（文分割後）：", "实际输入的目标文本(每句):": "実際に入力された目標テキスト（文ごと）：", "实际输入的目标文本:": "実際に入力された目標テキスト：", "导出文件格式": "エクスポートファイル形式", "已关闭": "閉じました", "已完成": "完了しました", "已开启": "有効化しました", "并行合成中": "並列合成処理中", "并行推理": "並列推論", "并行推理模式已关闭": "並列推論モードを無効化", "并行推理模式已开启": "並列推論モードを有効化", "底模缺失，无法加载相应 LoRA 权重": "ベースモデルが不足しているため、対応する LoRA の重みをロードできません", "开启": "有効化", "开启无参考文本模式。不填参考文本亦相当于开启。": "参照テキストなしモードを有効にします。参照テキストを入力しない場合も同様に有効になります。", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "並列推論モードが有効な場合、SoVITS V3/4モデルはバケット処理をサポートしないため、自動的に無効になっています。", "微调模型信息": "ファインチューニングモデル情報", "微调训练": "ファインチューニング", "怎么切": "どうやって切るか", "总训练轮数total_epoch": "総トレーニングエポック数total_epoch", "总训练轮数total_epoch，不建议太高": "総トレーニングエポック数total_epoch、高すぎないようにお勧めします", "指定输出主人声文件夹": "ボーカルの出力フォルダを指定:", "指定输出非主人声文件夹": "伴奏の出力フォルダを指定:", "按中文句号。切": "中国語の句点でカット", "按标点符号切": "句読点で分割", "按英文句号.切": "英文のピリオドで切ってください", "推理": "推論", "推理设置": "推論設定", "提取文本Bert特征": "テキストBERT特徴を抽出", "数据分桶(并行推理时会降低一点计算量)": "データバケット化（並列推論時に少し計算コストを減らす）", "数据类型精度": "データ型の精度", "文本分词与特征提取": "テキスト分割と特徴抽出", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "テキストセグメントツール。非常に長いテキストは合成結果が良くない可能性があるため、長いテキストは事前にセグメントすることをお勧びします。合成は改行に基づいて分かれ、その後に結合されます。", "文本模块学习率权重": "テキストモジュールの学習率の重み", "施工中，请静候佳音": "施工中、お待ちください", "日文": "日本語", "日英混合": "日英混合", "是否仅保存最新的权重文件以节省硬盘空间": "最新の重みファイルのみを保存し、ディスクスペースを節約しますか？", "是否在每次保存时间点将最终小模型保存至weights文件夹": "各保存時間点で最終的な小さなモデルをweightsフォルダに保存するかどうか", "是否开启DPO训练选项(实验性)": "DPO トレーニングオプションを有効にしますか？（実験的）", "是否直接对上次合成结果调整语速和音色。防止随机性。": "ランダム性を防ぐために、前回の合成結果のスピーチ速度とトーンを調整します。", "显卡信息": "グラフィックカード情報", "未下载模型": "モデルがダウンロードされていません", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "このソフトウェアはMITライセンスでオープンソース化されており、作者はソフトウェアに対して一切の制御権を持っていません。ソフトウェアを使用する者、ソフトウェアから導出される音声を広める者は、自己責任で行ってください。", "标注文件路径 (含文件后缀 *.list)": "ラベル付けファイルのパス（ファイル拡張子 *.list を含む）", "模型": "モデル", "模型分为三类：": "モデルは3種類に分かれています：", "模型切换": "モデル切り替え", "模型加载中，请等待": "モデルを読み込み中です。しばらくお待ちください...", "每张显卡的batch_size": "各グラフィックカードのバッチサイズ", "粤英混合": "粤英混合", "粤语": "粤語", "终止合成": "合成を終了", "缺少Hubert数据集": "Hubertデータセットが欠落しています", "缺少语义数据集": "セマンティクスデータセットが欠落しています", "缺少音素数据集": "音素データセットが欠落しています", "缺少音频数据集": "オーディオデータセットが欠落しています", "英文": "英語", "训练模型的版本": "学習済みモデルのバージョン", "训练集格式化一键三连": "トレーニングデータのフォーマットを一括処理", "训练集格式化工具": "トレーニングデータフォーマットツール", "语义Token提取": "セマンティックトークン抽出", "语速": "話速", "语速调整，高为更快": "話速調整、高いほど速く", "语速调节不支持分桶处理，已自动关闭分桶处理": "音声調整はバケット処理をサポートしていないため、自動的にバケット処理を無効化", "语音切分": "音声分割", "语音切分工具": "音声分割ツール", "语音文本校对标注工具": "音声テキスト校正・ラベル付けツール", "语音自监督特征提取": "音声自己教師あり特徴抽出", "语音识别": "音声認識", "语音识别工具": "音声認識ツール", "语音降噪": "音声ノイズ除去", "请上传3~10秒内参考音频，超过会报错！": "3～10秒以内の参照音声をアップロードしてください。それを超えるとエラーが発生します！", "请上传参考音频": "リファレンスオーディオをアップロードしてください", "请填入推理文本": "ターゲットテキストを入力してください", "请填入正确的List路径": "正しいリストパスを入力してください", "请填入正确的音频文件夹路径": "正しいオーディオフォルダパスを入力してください", "请输入有效文本": "有効なテキストを入力してください", "路径不存在,使用默认配置": "パスが見つからないため、デフォルト設定を使用", "路径不能为空": "空のパスは予期されていません", "路径错误": "パスエラー", "转换": "変換", "辅参考音频(可选多个，或不选)": "二次参考音声（複数可、またはなし）", "输入待处理音频文件夹路径": "処理するオーディオフォルダのパスを入力してください:", "输入文件夹路径": "入力フォルダのパス", "输入路径不存在": "入力パスが存在しません", "输入路径存在但不可用": "入力パスは存在しますが、使用できません", "输出logs/实验名目录下应有23456开头的文件和文件夹": "logs/実験名ディレクトリには23456で始まるファイルとフォルダが含まれている必要があります", "输出信息": "出力情報", "输出文件夹路径": "出力フォルダのパス", "输出的语音": "推論結果", "运行中": "実行中", "进度": "進捗", "进程已终止": "プロセスが終了しました", "进程输出信息": "プロセスの出力情報", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "SoVITS_weightsとGPT_weightsに保存された学習済みモデルを選択してください。デフォルトのモデルはベースモデルで、5秒Zero Shot TTS推論を学習なしで体験できます。", "采样步数(仅对V3/4生效)": "サンプリングステップ数（V3/V4のみ有効）", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "サンプリングステップ数：ノイズが強い場合は増やし、遅い場合は減らしてみてください", "重复惩罚": "繰り返しペナルティ", "随机种子": "ランダムシード", "需先终止才能开启下一次任务": "次のタスクを開始する前に、現在のタスクを終了する必要があります", "需要合成的切分前文本": "セグメント前の推論テキスト", "需要合成的文本": "推論テキスト", "需要合成的文本的语种": "推論テキストの言語", "需要合成的语种": "推論の言語", "韩文": "韓国語", "韩英混合": "韓英混合", "音频加载失败": "音声の読み込みに失敗しました", "音频文件不存在，跳过：": "音声ファイルが見つかりません。スキップします：", "音频标注WebUI": "音声アノテーション用WebUI", "音频自动切分输入路径，可文件可文件夹": "音声自動分割の入力パス（ファイルまたはフォルダ指定可）", "音频超分中": "音声スーパーレゾリューション処理中", "音频超采样": "音声アップサンプリング", "音频超采样(仅对V3生效))": "音声アップサンプリング（V3のみ有効）", "预测语义Token": "意味的トークンを予測する", "预训练GPT模型路径": "事前学習済みGPTモデルのパス", "预训练SSL模型路径": "事前学習済みSSLモデルのパス", "预训练SoVITS-D模型路径": "事前学習済みSoVITS-Dモデルのパス", "预训练SoVITS-G模型路径": "事前学習済みSoVITS-Gモデルのパス", "预训练中文BERT模型路径": "事前学習済み中国語BERTモデルのパス", "预训练模型路径": "事前学習済みモデルのパス"}