{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net (onnx_dereverb): reverberación estéreo, la mejor opción; no puede eliminar reverberación mono", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho: Eliminar el efecto de retardo. Aggressive elimina más que Normal, DeReverb elimina reverberación adicional, puede eliminar reverberación mono, pero no limpia bien la reverberación de placa de alta frecuencia", "*实验/模型名": "*Nombre del experimento/modelo", "*文本标注文件": "*Archivo de etiquetado de texto", "*训练集音频文件目录": "*Directorio de archivos de audio de entrenamiento", "*请上传并填写参考信息": "*Por favor, suba y complete la información de referencia", "*请填写需要合成的目标文本和语种模式": "*Por favor, complete el texto objetivo a sintetizar y el modo de idioma", ".限制范围越小判别效果越好。": ".Cuanto más pequeño sea el rango, mejor será el efecto de discriminación.", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. El modelo DeEcho-DeReverb tarda casi el doble que los otros dos modelos DeEcho", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. <PERSON><PERSON><PERSON> voz principal: seleccione este para audio sin coros, retiene mejor la voz principal que HP5. Incluye dos modelos, HP2 y HP3; HP3 puede filtrar ligeramente el acompañamiento pero retiene mejor la voz principal que HP2", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Cambio de voz", "2、MDX-Net-Dereverb模型挺慢的；": "2. El modelo MDX-Net-Dereverb es bastante lento", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. Solo retener voz principal: seleccione este para audio con coros, puede debilitar la voz principal. Incluye un modelo HP5", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. La configuración más limpia recomendada es primero MDX-Net, luego DeEcho-Aggressive", "3、去混响、去延迟模型（by FoxJoy）：": "3. Modelos de eliminación de reverberación y retardo (por FoxJoy)", "ASR 模型": "Modelo ASR", "ASR 模型尺寸": "Tamaño del modelo ASR", "ASR 语言设置": "Configuración del idioma ASR", "CPU训练,较慢": "Entrenamiento en CPU, más lento", "GPT 训练: 模型权重文件在 GPT_weights/": "Entrenamiento de GPT: los archivos de pesos del modelo están en GPT_weights/", "GPT模型列表": "Lista de modelos GPT", "GPT训练": "Entrenamiento de GPT", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "Parámetros de muestreo de GPT (no demasiado bajos cuando no hay texto de referencia. Use los valores por defecto si no está seguro):", "GPU卡号,只能填1个整数": "Número de tarjeta GPU, solo se puede ingresar un número entero", "GPU卡号以-分割，每个卡号一个进程": "Número de tarjeta GPU separado por '-', cada número de tarjeta es un proceso", "LoRA秩": "<PERSON><PERSON>", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "Entrenamiento de SoVITS: los archivos de pesos del modelo están en SoVITS_weights/", "SoVITS模型列表": "Lista de modelos SoVITS", "SoVITS训练": "Entrenamiento de SoVITS", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "Enviar texto: Guarda manualmente el contenido de todos los campos de texto en la página actual en memoria y archivo (si no haces clic en este botón antes o después de cambiar de página o salir de la página de etiquetado, al regresar se desharán los cambios, ¡todo ese trabajo se perderá!).", "TTS推理WebUI": "WebUI de inferencia TTS", "UVR5人声伴奏分离&去混响去延迟工具": "Herramienta de separación de voz y acompañamiento UVR5 y eliminación de reverberación y retardo", "V3不支持无参考文本模式，请填写参考文本！": "¡V3 no admite el modo sin texto de referencia! Por favor, introduce el texto de referencia.", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: proporción de mezcla de audio normalizado que entra", "batch_size": "<PERSON><PERSON><PERSON> de lote", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: cómo calcular la curva de volumen, cuanto más pequeño, mayor precisión pero mayor carga computacional (mayor precisión no significa mejor rendimiento)", "max:归一化后最大值多少": "max: valor máximo después de la normalización", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: duración máxima del silencio después del corte", "min_interval:最短切割间隔": "min_interval: intervalo mínimo de corte", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: longitud mínima de cada segmento; si el primer segmento es demasiado corto, se une al siguiente hasta superar este valor", "temperature": "temperatura", "threshold:音量小于这个值视作静音的备选切割点": "umbral: puntos de corte alternativos considerados como silencio si el volumen es menor que este valor", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "v3 no es compatible con este modo actualmente y su uso generará un error.", "v3输出如果觉得闷可以试试开超分": "Si la salida de V3 parece aburrida, puedes intentar activar la superresolución", "不切": "No cortar", "不训练直接推v2ProPlus底模！": "¡Usar directamente el modelo base v2ProPlus sin entrenar!", "不训练直接推v2Pro底模！": "¡Usar directamente el modelo base v2Pro sin entrenar!", "不训练直接推v2底模！": "¡Usar directamente el modelo base v2 sin entrenar!", "不训练直接推v3底模！": "¡Usar directamente el modelo base v3 sin entrenar!", "中文": "Chino", "中英混合": "Chino e inglés mezclados", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "Audio de referencia principal (Por favor, suba un audio de referencia de entre 3 y 10 segundos, si supera este límite se producirá un error)", "主参考音频的文本": "Texto del audio de referencia principal", "主参考音频的语种": "Idioma del audio de referencia principal", "也可批量输入音频文件, 二选一, 优先读文件夹": "También se pueden ingresar archivos de audio por lotes, seleccionar uno, prioridad para leer carpetas", "人声伴奏分离批量处理， 使用UVR5模型。": "Procesamiento por lotes de separación de voz y acompañamiento utilizando el modelo UVR5", "人声分离WebUI": "WebUI de separación de voces", "人声提取激进程度": "Nivel de agresividad en la extracción de voz", "以下文件或文件夹不存在": "No Existe Tal Archivo o Carpeta", "以下模型不存在:": "No Existe tal Modelo:", "伴奏人声分离&去混响&去回声": "Separación de acompañamiento y voz principal y eliminación de reverberación y eco", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "No has descargado los parámetros del modelo de superresolución, por lo que no se realizará la superresolución. Si deseas habilitarla, sigue el tutorial y descarga los archivos necesarios", "使用无参考文本模式时建议使用微调的GPT": "Se recomienda usar un GPT ajustado cuando se use el modo sin texto de referencia.", "保存频率save_every_epoch": "Frecuencia de guardado (cada epoch)", "保持随机": "Mantener aleatorio", "关闭": "<PERSON><PERSON><PERSON> ", "凑50字一切": "Todo para alcanzar las 50 palabras", "凑四句一切": "Completa cuatro oraciones para rellenar todo", "分桶处理模式已关闭": "Modo de procesamiento por lotes deshabilitado", "分桶处理模式已开启": "Modo de procesamiento por lotes habilitado", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "El modo de retorno segmentado no es compatible con el procesamiento por lotes, se ha deshabilitado automáticamente", "分段返回模式已开启": "Modo de retorno segmentado habilitado", "分段间隔(秒)": "Intervalo de segmentación (segundos)", "分段间隔过小，已自动设置为0.01": "El intervalo de segmentación es demasiado pequeño, se ha ajustado automáticamente a 0.01", "切分": "Segmentación", "切分后文本": "Texto después de la segmentación", "切分后的子音频的输出根目录": "Directorio raíz de salida de los sub-audios después de la división", "切分文本": "Segmentar texto", "切割使用的进程数": "Número de procesos utilizados para la división", "刷新模型路径": "Actualizar la ruta del modelo", "前端处理后的文本(每句):": "Texto después del procesamiento previo (por frase):", "前置数据集获取工具": "Herramienta de adquisición de conjunto de datos previo", "占用中": " En uso", "去混响/去延迟，附：": "Eliminación de reverberación/retardo, incluye:", "参考音频在3~10秒范围外，请更换！": "El audio de referencia está fuera del rango de 3 a 10 segundos, ¡por favor cámbielo!", "参考音频的文本": "Texto de referencia del audio", "参考音频的语种": "Idioma del audio de referencia", "句间停顿秒数": "Segundos de pausa entre frases", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Opcional: Sube varios archivos de audio de referencia arrastrándolos y soltándolos (se recomienda que sean del mismo género) y promedia sus tonos. Si esta opción se deja en blanco, el tono será controlado por el único audio de referencia a la izquierda. Si se está afinando el modelo, se recomienda que todos los archivos de audio de referencia tengan tonos dentro del conjunto de entrenamiento de ajuste fino; se puede ignorar el modelo preentrenado.", "合成语音": "Sín<PERSON>is de voz", "合成音频": "Sintetizar Audio", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Ejemplo de formato de ruta de carpeta válida: E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (simplemente copie desde la barra de direcciones del administrador de archivos).", "后续将支持转音素、手工修改音素、语音合成分步执行。": "Se añadirá soporte para conversión de fonemas, edición manual de fonemas y síntesis de voz por pasos en el futuro.", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "Si el audio de referencia no es claro o no sabe qué escribir, habilite esta opción para ignorar el texto de referencia.", "启用并行推理版本": "Habilitar versión de inferencia paralela", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Ingrese el directorio donde se encuentran los audios después de la división. La ruta completa de los archivos de audio leídos = este directorio + nombre de archivo correspondiente en el archivo .list (no la ruta completa). Si se deja en blanco, se utilizará la ruta completa del archivo .list.", "多语种混合": "Mezcla de varios idiomas", "多语种混合(粤语)": "<PERSON><PERSON><PERSON><PERSON> (Cantonés)", "失败": " Fallido", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "Si no acepta estos términos, no puede utilizar ni hacer referencia a ningún código o archivo dentro del paquete de software. Consulte el archivo LICENSE en el directorio raíz para obtener más detalles.", "实际输入的参考文本:": "Texto de referencia realmente ingresado:", "实际输入的目标文本(切句后):": "Texto objetivo realmente ingresado (después de dividir en frases):", "实际输入的目标文本(每句):": "Texto objetivo realmente ingresado (por frase):", "实际输入的目标文本:": "Texto objetivo realmente ingresado:", "导出文件格式": "Formato de archivo de exportación", "已关闭": " Desactivado", "已完成": " Completado", "已开启": " Activado", "并行合成中": "Síntesis en paralelo en curso", "并行推理": "Inferencia paralela", "并行推理模式已关闭": "Modo de inferencia paralela deshabilitado", "并行推理模式已开启": "Modo de inferencia paralela habilitado", "底模缺失，无法加载相应 LoRA 权重": "Falta el modelo base, no se pueden cargar los pesos de LoRA correspondientes", "开启": "Activar ", "开启无参考文本模式。不填参考文本亦相当于开启。": "Habilitar el modo sin texto de referencia. No llenar el texto de referencia también lo habilita.", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "Al activar el modo de inferencia paralela, los modelos SoVITS V3/4 no admiten el procesamiento por lotes, por lo que este ha sido desactivado automáticamente.", "微调模型信息": "Información del modelo fine-tuned", "微调训练": "Entrenamiento de ajuste fino", "怎么切": "Cómo cortar", "总训练轮数total_epoch": "Número total de épocas de entrenamiento", "总训练轮数total_epoch，不建议太高": "Número total de épocas de entrenamiento, no se recomienda demasiado alto", "指定输出主人声文件夹": "Especificar carpeta de salida de voz principal", "指定输出非主人声文件夹": "Especificar carpeta de salida de no voz principal", "按中文句号。切": "Cortar según puntos en chino", "按标点符号切": "Cortar según los signos de puntuación", "按英文句号.切": "Cortar por puntos en inglés.", "推理": "Inferencia", "推理设置": "Configuración de inferencia", "提取文本Bert特征": "Extraer características de texto con BERT", "数据分桶(并行推理时会降低一点计算量)": "Agrupación de datos (Reduce el costo computacional en inferencia paralela)", "数据类型精度": "precisión del tipo de datos", "文本分词与特征提取": "Segmentación de texto y extracción de características", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "Herramienta de segmentación de texto. Un texto demasiado largo puede no producir buenos resultados, por lo que se recomienda segmentarlo. La síntesis se realizará por separado según los saltos de línea y luego se unirá.", "文本模块学习率权重": "Peso de la tasa de aprendizaje del módulo de texto", "施工中，请静候佳音": "En construcción, por favor espere pacientemente", "日文": "Japonés", "日英混合": "Mezcla de japonés e inglés", "是否仅保存最新的权重文件以节省硬盘空间": "¿Guardar solo el último archivo de pesos más reciente para ahorrar espacio en disco?", "是否在每次保存时间点将最终小模型保存至weights文件夹": "¿Guardar el modelo final pequeño en la carpeta de pesos en cada punto de guardado?", "是否开启DPO训练选项(实验性)": "¿Habilitar la opción de entrenamiento dpo (experimental)?", "是否直接对上次合成结果调整语速和音色。防止随机性。": "¿Ajustar directamente la velocidad del habla y el tono del último resultado de síntesis? Para prevenir la aleatoriedad.", "显卡信息": "Información de la tarjeta gráfica", "未下载模型": "Modelo no descargado", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "Este software es de código abierto bajo la licencia MIT. El autor no tiene control sobre el software. El usuario que lo utilice o distribuya, y el que genere sonidos a partir del software, asume toda la responsabilidad.", "标注文件路径 (含文件后缀 *.list)": "Ruta del archivo de anotaciones (con extensión *.list)", "模型": "<PERSON><PERSON>", "模型分为三类：": "Los modelos se dividen en tres categorías:", "模型切换": "Cambio de modelo", "模型加载中，请等待": "El modelo se está cargando, por favor espera...", "每张显卡的batch_size": "Tamaño de lote por tarjeta gráfica", "粤英混合": "Mezcla Cantonés-Inglés", "粤语": "Cantonés", "终止合成": "<PERSON><PERSON><PERSON><PERSON>", "缺少Hubert数据集": "Falta el Conjunto de Datos de Hubert", "缺少语义数据集": "Falta el Conjunto de Datos Semánticos", "缺少音素数据集": "Falta el Conjunto de Datos de Fonemas", "缺少音频数据集": "Falta el Conjunto de Datos de Audio", "英文": "Inglés", "训练模型的版本": "Versión del modelo entrenado", "训练集格式化一键三连": "Formato del conjunto de entrenamiento en un solo paso", "训练集格式化工具": "Herramienta de formateo del conjunto de datos de entrenamiento", "语义Token提取": "Extracción de tokens semánticos", "语速": "Velocidad de <PERSON>bla", "语速调整，高为更快": "Ajustar la velocidad de habla, más alta para más rápido", "语速调节不支持分桶处理，已自动关闭分桶处理": "El ajuste de velocidad de voz no es compatible con el procesamiento por lotes, se ha deshabilitado automáticamente", "语音切分": "Segmentación de voz", "语音切分工具": "Herramienta de división de voz", "语音文本校对标注工具": "Herramienta de corrección y anotación de texto de voz", "语音自监督特征提取": "Extracción de características de voz con auto-supervisión", "语音识别": "Reconocimiento de voz", "语音识别工具": "Herramienta de reconocimiento de voz", "语音降噪": "Reducción de ruido en la voz", "请上传3~10秒内参考音频，超过会报错！": "Por favor, suba un audio de referencia de entre 3 y 10 segundos, ¡más de eso causará un error!", "请上传参考音频": "<PERSON><PERSON>, Suba el Audio de Referencia", "请填入推理文本": "<PERSON><PERSON>, Ingrese el Texto Objetivo", "请填入正确的List路径": "<PERSON><PERSON> F<PERSON>, Introduzca la Ruta Correcta de la Lista", "请填入正确的音频文件夹路径": "<PERSON><PERSON> Fav<PERSON>, Introduzca la Ruta Correcta de la Carpeta de Audio", "请输入有效文本": "Por favor, introduzca un texto válido", "路径不存在,使用默认配置": "Ruta no encontrada, usando configuración predeterminada", "路径不能为空": "Se Espera que la Ruta No Esté Vacía", "路径错误": "<PERSON><PERSON><PERSON>", "转换": "Convertir", "辅参考音频(可选多个，或不选)": "Audio de referencia secundario (Opcional, se pueden seleccionar varios o ninguno)", "输入待处理音频文件夹路径": "Ingrese la ruta de la carpeta de audio a procesar", "输入文件夹路径": "Ingrese la ruta de la carpeta", "输入路径不存在": "La ruta de entrada no existe", "输入路径存在但不可用": "La ruta de entrada existe pero no es accesible", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Debe haber archivos y carpetas que comiencen con 23456 en el directorio logs/nombre del experimento", "输出信息": "Información de salida", "输出文件夹路径": "<PERSON>uta de la carpeta de salida", "输出的语音": "Audio de salida", "运行中": " En ejecución", "进度": "Progreso", "进程已终止": " Proceso terminado", "进程输出信息": " Información de salida del proceso", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "Seleccione los modelos guardados en SoVITS_weights y GPT_weights después del entrenamiento. Los predeterminados son modelos base para experimentar con inferencia Zero Shot TTS de 5 segundos sin entrenamiento.", "采样步数(仅对V3/4生效)": "Pasos de muestreo (solo efectivo para V3/4)", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "Pasos de muestreo: si se siente ruidoso, intente aumentarlo; si es lento, intente reducirlo", "重复惩罚": "Penalización por repetición", "随机种子": "<PERSON>lla aleatoria", "需先终止才能开启下一次任务": " Debe terminarse antes de iniciar la siguiente tarea", "需要合成的切分前文本": "Texto antes de la segmentación para la síntesis", "需要合成的文本": "Texto a sintetizar", "需要合成的文本的语种": "Idioma del texto a sintetizar", "需要合成的语种": "Idioma para la síntesis", "韩文": "<PERSON><PERSON>", "韩英混合": "<PERSON><PERSON><PERSON><PERSON>-Inglés", "音频加载失败": "Error al Cargar el Audio", "音频文件不存在，跳过：": "Archivo de audio no encontrado, omitiendo: ", "音频标注WebUI": "WebUI de etiquetado de audio", "音频自动切分输入路径，可文件可文件夹": "Ruta de entrada para la división automática de audio, puede ser un archivo o una carpeta", "音频超分中": "Superresolución de audio en proceso", "音频超采样": "Muestreo superior del audio", "音频超采样(仅对V3生效))": "Muestreo superior del audio (solo efectivo para V3)", "预测语义Token": "Predecir token semán<PERSON>o", "预训练GPT模型路径": "Ruta del modelo GPT preentrenado", "预训练SSL模型路径": "Ruta del modelo SSL preentrenado", "预训练SoVITS-D模型路径": "Ruta del modelo SoVITS-D preentrenado", "预训练SoVITS-G模型路径": "Ruta del modelo SoVITS-G preentrenado", "预训练中文BERT模型路径": "Ruta del modelo BERT Chino Preentrenado", "预训练模型路径": "Ruta del modelo preentrenado"}