{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb):Это лучший выбор для реверберации с двумя каналами, но он не может устранить реверберацию с одним каналом;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:Устраняет эффект задержки. Aggressive устраняет более тщательно, чем Normal, DeReverb дополнительно устраняет реверберацию, может устранить реверберацию с одного канала, но не полностью устраняет высокочастотную реверберацию.", "*实验/模型名": "*Название эксперимента/модели", "*文本标注文件": "*Файл текстовой аннотации", "*训练集音频文件目录": "*Директория аудиофайлов обучающего набора", "*请上传并填写参考信息": "*Пожалуйста, загрузите и заполните референтные данные", "*请填写需要合成的目标文本和语种模式": "*Пожалуйста, введите целевой текст для синтеза и режим языка", ".限制范围越小判别效果越好。": "Чем меньше языков, тем лучше", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. Время обработки модели De<PERSON>-DeReverb почти вдвое больше, чем у двух других моделей DeEcho;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Сохранение голоса: выберите этот для аудио без гармоний, сохранение голоса будет лучше, чем HP5. Встроенные модели HP2 и HP3, HP3 может немного пропускать сопровождение, но сохраняет голос немного лучше, чем HP2;", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-переозвучивание", "2、MDX-Net-Dereverb模型挺慢的；": "2. Модель MDX-Net-Dereverb довольно медленная;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. Сохранение только основного голоса: выберите это для аудио с гармониями, может ослабить основной голос. Встроенная модель HP5;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. Лично рекомендованная самая чистая конфигурация — сначала MDX-Net, затем DeEcho-Aggressive.", "3、去混响、去延迟模型（by FoxJoy）：": "3. Модель удаления реверберации и задержек (от FoxJoy):", "ASR 模型": "Модель ASR", "ASR 模型尺寸": "Размер модели ASR", "ASR 语言设置": "Настройки языка ASR", "CPU训练,较慢": "Обучение на CPU (медленнее)", "GPT 训练: 模型权重文件在 GPT_weights/": "Обучение GPT: файлы весов модели находятся в GPT_weights/", "GPT模型列表": "Список моделей GPT", "GPT训练": "Обучение GPT", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "Параметры выборки GPT (не устанавливайте слишком низкие значения, если нет ссылочного текста. Используйте значения по умолчанию, если не уверены):", "GPU卡号,只能填1个整数": "Номер GPU, можно указать только одно целое число", "GPU卡号以-分割，每个卡号一个进程": "Номера GPU разделяются дефисом, на каждый номер отдельный процесс", "LoRA秩": "Ранг <PERSON>", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "Обучение SoVITS: файлы весов модели находятся в SoVITS_weights/", "SoVITS模型列表": "Список моделей SoVITS", "SoVITS训练": "Обучение SoVITS", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "Отправить текст: Вручную сохраните содержимое всех текстовых полей текущей страницы в память и файл. Если вы не нажмете эту кнопку до или после смены страницы или перед выходом из интерфейса разметки, при возврате все изменения будут отменены — работа пропадет зря.", "TTS推理WebUI": "TTS WebUI для инференса", "UVR5人声伴奏分离&去混响去延迟工具": "UVR5 Инструмент разделения вокала и сопровождения & удаления реверберации и задержки", "V3不支持无参考文本模式，请填写参考文本！": "V3 не поддерживает режим без опорного текста. Пожалуйста, укажите опорный текст!", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix:Какая доля нормализованного аудио смешивается", "batch_size": "размер пакета", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size:Как рассчитывается кривая громкости, чем меньше, тем выше точность и больше вычислительная нагрузка (большая точность не всегда означает лучший результат)", "max:归一化后最大值多少": "max:Максимальное значение после нормализации", "max_sil_kept:切完后静音最多留多长": "max_sil_kept:Максимальная длительность тишины после разреза", "min_interval:最短切割间隔": "min_interval:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ный интервал разреза", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length:Минимальная длина каждого отрезка; если первый отрезок слишком короткий, он будет соединен с последующими до достижения этого значения", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "threshold:Значение громкости ниже этого считается тишиной для альтернативной точки разреза", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "v3 пока не поддерживает этот режим, при использовании возникнет ошибка.", "v3输出如果觉得闷可以试试开超分": "Если вывод v3 кажется глухим, попробуйте включить супер-разрешение", "不切": "Не разрезать", "不训练直接推v2ProPlus底模！": "Использовать базовую модель v2ProPlus без обучения!", "不训练直接推v2Pro底模！": "Использовать базовую модель v2Pro без обучения!", "不训练直接推v2底模！": "Использовать базовую модель v2 без обучения!", "不训练直接推v3底模！": "Использовать базовую модель v3 без обучения!", "中文": "Китайский", "中英混合": "Китайский и английский", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "Основной опорный аудиофайл (пожалуйста, загрузите опорный аудиофайл длительностью от 3 до 10 секунд, превышение этого лимита вызовет ошибку!)", "主参考音频的文本": "Текст основного опорного аудиофайла", "主参考音频的语种": "Язык основного опорного аудиофайла", "也可批量输入音频文件, 二选一, 优先读文件夹": "Можно также импортировать несколько аудиофайлов. Если путь к папке существует, то этот ввод игнорируется.", "人声伴奏分离批量处理， 使用UVR5模型。": "Обработка разделения вокала и аккомпанемента пакетно с использованием модели UVR5.", "人声分离WebUI": "WebUI для разделения вокала", "人声提取激进程度": "Степень агрессивности извлечения вокала", "以下文件或文件夹不存在": "Такого файла или папки не существует", "以下模型不存在:": "Этот модель не существует", "伴奏人声分离&去混响&去回声": "Разделение вокала/аккомпанемента и удаление эхо", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "Параметры модели супер-разрешения не загружены, поэтому супер-разрешение не будет выполняться. Чтобы использовать его, сначала загрузите необходимые файлы, следуя руководству.", "使用无参考文本模式时建议使用微调的GPT": "При использовании режима без эталонного текста рекомендуется использовать дообученный GPT.", "保存频率save_every_epoch": "Частота сохранения save_every_epoch", "保持随机": "Сохранить случайное значение", "关闭": "Закрыть ", "凑50字一切": "Соберите все в 50 символов", "凑四句一切": "Собрать четыре предложения и разрезать", "分桶处理模式已关闭": "Режим обработки пакетов отключен", "分桶处理模式已开启": "Режим обработки пакетов включен", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "Режим сегментированного возврата не поддерживает обработку пакетов, обработка пакетов отключена автоматически", "分段返回模式已开启": "Режим сегментированного возврата включен", "分段间隔(秒)": "Интервал сегментации (секунды)", "分段间隔过小，已自动设置为0.01": "Интервал сегментации слишком короткий, автоматически установлен на 0.01", "切分": "Сегментация", "切分后文本": "Текст для синтеза после сегментации", "切分后的子音频的输出根目录": "Корневой каталог вывода для подаудио после разделения", "切分文本": "Сегментировать текст", "切割使用的进程数": "Количество процессов, используемых для разрезания", "刷新模型路径": "Обновить путь к модели", "前端处理后的文本(每句):": "Текст после предварительной обработки (каждое предложение):", "前置数据集获取工具": "Инструмент для получения предварительных данных", "占用中": " Занято", "去混响/去延迟，附：": "Удаление реверберации/удаление задержки, примечание:", "参考音频在3~10秒范围外，请更换！": "Референтное аудио вне диапазона 3~10 секунд, пожалуйста, замените!", "参考音频的文本": "Текст референтного аудио", "参考音频的语种": "Язык референтного аудио", "句间停顿秒数": "Время паузы между предложениями (в секундах)", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Необязательно: загрузите несколько файлов с эталонными аудиозаписями, перетащив их (рекомендуется одного пола), и усредните их тон. Если этот параметр не заполнен, тон будет контролироваться одной эталонной аудиозаписью слева. При тонкой настройке модели рекомендуется, чтобы все эталонные аудиозаписи имели тон в пределах обучающего набора для тонкой настройки; предварительно обученную модель можно игнорировать.", "合成语音": "Синтезированный голос", "合成音频": "Синтезировать аудио", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Пример допустимого формата пути к папке: E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (просто скопируйте из адресной строки файлового менеджера).", "后续将支持转音素、手工修改音素、语音合成分步执行。": "В будущем будет добавлена поддержка преобразования в фонемы, ручного редактирования фонемов и пошагового выполнения синтеза речи.", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "Если невозможно разобрать речь в эталонном аудио (и непонятно, что писать), можно включить эту опцию. При включении вводимый эталонный текст будет игнорироваться.", "启用并行推理版本": "Включить параллельную версию вывода", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Заполните каталог, где находятся аудиофайлы после разрезания! Полный путь к читаемым аудиофайлам = каталог - файл .list, имя файла соответствует волне (не полный путь). Если оставить пустым, будет использоваться абсолютный путь из файла .list.", "多语种混合": "Смешанные языки", "多语种混合(粤语)": "Многоязычная смесь (кантонский)", "失败": " Неудача", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "Если вы не согласны с этими условиями, вы не можете использовать или ссылаться на любой код или файлы из этого программного пакета. Подробности смотрите в LICENSE в корневом каталоге.", "实际输入的参考文本:": "Фактически введенный референсный текст:", "实际输入的目标文本(切句后):": "Фактически введенный целевой текст (после разбиения на предложения):", "实际输入的目标文本(每句):": "Фактически введенный целевой текст (каждое предложение):", "实际输入的目标文本:": "Фактически введенный целевой текст:", "导出文件格式": "Формат выходных файлов", "已关闭": " Закрыто", "已完成": " Завершено", "已开启": " Включено", "并行合成中": "Синтез в параллельном режиме", "并行推理": "Параллельный вывод", "并行推理模式已关闭": "Режим параллельного вывода отключен", "并行推理模式已开启": "Режим параллельного вывода включен", "底模缺失，无法加载相应 LoRA 权重": "Отсутствует базовая модель, не удалось загрузить соответствующие веса LoRA.", "开启": "Включить ", "开启无参考文本模式。不填参考文本亦相当于开启。": "Включить режим без референтного текста. Не заполняя референтный текст, вы также включаете этот режим.", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "При включенном режиме параллельного вывода модель SoVits V3/4 не поддерживает обработку по бакетам, эта функция была автоматически отключена.", "微调模型信息": "Информация о дообученной модели", "微调训练": "Обучение с тонкой настройкой", "怎么切": "Как разрезать", "总训练轮数total_epoch": "Общее количество эпох обучения total_epoch", "总训练轮数total_epoch，不建议太高": "Общее количество эпох обучения total_epoch, не рекомендуется слишком высокое", "指定输出主人声文件夹": "Путь к папке для сохранения вокала:", "指定输出非主人声文件夹": "Путь к папке для сохранения аккомпанемента:", "按中文句号。切": "Разделение по китайским точкам.", "按标点符号切": "Разрезать по пунктуационным знакам", "按英文句号.切": "Разрезать по английской точке.", "推理": "Инференс", "推理设置": "Настройки вывода", "提取文本Bert特征": "Извлечь текстовые признаки BERT", "数据分桶(并行推理时会降低一点计算量)": "Разбиение данных на пакеты (уменьшает вычислительные затраты при параллельном выводе)", "数据类型精度": "точность типа данных", "文本分词与特征提取": "Токенизация текста и извлечение признаков", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "Инструмент для сегментации текста. Очень длинный текст может не дать хороших результатов синтеза, поэтому рекомендуется сначала разбить его на сегменты. Синтез будет выполняться на основе разрывов строк, а затем объединяться.", "文本模块学习率权重": "Веса скорости обучения текстового модуля", "施工中，请静候佳音": "В разработке, ожидайте хороших новостей", "日文": "Японский", "日英混合": "Японский и английский", "是否仅保存最新的权重文件以节省硬盘空间": "Сохранить только последние файлы весов для экономии дискового пространства?", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Сохранять финальную версию модели в папке weights на каждом этапе сохранения?", "是否开启DPO训练选项(实验性)": "Включить опцию обучения DPO (экспериментально)?", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Настройте скорость речи и тон последнего результата синтеза, чтобы избежать случайности.", "显卡信息": "Информация о видеокарте", "未下载模型": "Модель не загружена", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "Эта программа распространяется с открытым исходным кодом по лицензии MIT, и автор не несёт ответственности за её использование. Пользователь и распространитель звука, созданного программой, несут полную ответственность.", "标注文件路径 (含文件后缀 *.list)": "Путь к файлу разметки (с расширением *.list)", "模型": "Модели", "模型分为三类：": "Модели делятся на три типа:", "模型切换": "Переключение модели", "模型加载中，请等待": "Модель загружается, пожалуйста, подождите...", "每张显卡的batch_size": "Размер пакета для каждой видеокарты", "粤英混合": "Кантоно-английская смесь", "粤语": "Кантонийский", "终止合成": "Завершить синтез", "缺少Hubert数据集": "Отсутствует набор данных Hubert", "缺少语义数据集": "Отсутствует семантический набор данных", "缺少音素数据集": "Отсутствует набор данных фонем", "缺少音频数据集": "Отсутствует набор данных аудио", "英文": "Английский", "训练模型的版本": "Версия обученной модели", "训练集格式化一键三连": "Форматирование обучающего набора одним кликом", "训练集格式化工具": "Инструмент форматирования обучающего набора", "语义Token提取": "Извлечение семантических токенов", "语速": "Скорость речи", "语速调整，高为更快": "Регулировка скорости речи, чем выше, тем быстрее", "语速调节不支持分桶处理，已自动关闭分桶处理": "Регулировка скорости речи не поддерживает обработку пакетов, обработка пакетов отключена автоматически", "语音切分": "Разделение аудиофайла", "语音切分工具": "Инструмент разделения аудиофа<PERSON>лов", "语音文本校对标注工具": "Инструмент аннотирования и корректировки аудиотекста", "语音自监督特征提取": "Извлечение характеристик самообучающейся модели речи", "语音识别": "Распознавание речи", "语音识别工具": "Инструмент распознавания речи", "语音降噪": "Шумоподавление аудиофайлов", "请上传3~10秒内参考音频，超过会报错！": "Пожалуйста, загрузите референтное аудио длительностью от 3 до 10 секунд, иначе будет ошибка!", "请上传参考音频": "Пожалуйста, загрузите эталонное аудио", "请填入推理文本": "Пожалуйста, введите целевой текст", "请填入正确的List路径": "Пожалуйста, укажите правильный путь к списку", "请填入正确的音频文件夹路径": "Пожалуйста, укажите правильный путь к папке с аудио", "请输入有效文本": "Введите действительный текст", "路径不存在,使用默认配置": "Путь не найден, используется конфигурация по умолчанию", "路径不能为空": "Ожида<PERSON><PERSON><PERSON>я, что путь не будет пустым", "路径错误": "Ошибка пути", "转换": "Преобразовать", "辅参考音频(可选多个，或不选)": "Дополнительный опорный аудиофайл (несколько файлов по желанию или ни одного)", "输入待处理音频文件夹路径": "Путь к папке с аудиофайлами для обработки:", "输入文件夹路径": "Введите путь к папке", "输入路径不存在": "Путь ввода не существует", "输入路径存在但不可用": "Путь ввода существует, но недоступен", "输出logs/实验名目录下应有23456开头的文件和文件夹": "В директории logs/имя_эксперимента должны быть файлы и папки, начинающиеся с 23456", "输出信息": "Статистика", "输出文件夹路径": "Путь к папке для вывода", "输出的语音": "Выводимый звук", "运行中": " Выполняется", "进度": "Прогресс", "进程已终止": " Процесс завершён", "进程输出信息": " Выходные данные процесса", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "Выберите обученные модели в папках SoVITS_weights и GPT_weights. По умолчанию доступны базовые модели для Zero Shot TTS-синтеза за 5 секунд без обучения.", "采样步数(仅对V3/4生效)": "Число шагов выборки (действительно только для V3/4)", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "Количество шагов выборки: если кажется, что шумно, попробуйте увеличить, если кажется, что медленно, попробуйте уменьшить", "重复惩罚": "Штраф за повторение", "随机种子": "Случайное начальное значение", "需先终止才能开启下一次任务": "Сначала необходимо завершить текущую задачу, прежде чем запускать новую", "需要合成的切分前文本": "Текст для синтеза до сегментации", "需要合成的文本": "Текст для синтеза", "需要合成的文本的语种": "Язык текста для синтеза", "需要合成的语种": "Язык для синтеза", "韩文": "Корейский", "韩英混合": "Корейско-английская смесь", "音频加载失败": "Не удалось загрузить аудио", "音频文件不存在，跳过：": "Файл аудио не найден, пропускается: ", "音频标注WebUI": "Веб-интерфейс разметки аудио", "音频自动切分输入路径，可文件可文件夹": "Путь ввода для автоматического разделения аудио, может быть файлом или папкой", "音频超分中": "Супер-разрешение аудио в процессе", "音频超采样": "Апсэмплирование аудио", "音频超采样(仅对V3生效))": "Апсэмплирование аудио (действительно только для V3)", "预测语义Token": "Предсказать семантический токен", "预训练GPT模型路径": "Путь к предобученной модели GPT", "预训练SSL模型路径": "Путь к предобученной модели SSL", "预训练SoVITS-D模型路径": "Путь к предобученной модели SoVITS-D", "预训练SoVITS-G模型路径": "Путь к предобученной модели SoVITS-G", "预训练中文BERT模型路径": "Путь к предобученной китайской модели BERT", "预训练模型路径": "Путь к предобученной модели"}