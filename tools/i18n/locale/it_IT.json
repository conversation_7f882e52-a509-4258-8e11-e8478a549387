{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net (onnx_dereverb): È la scelta migliore per la riverberazione a due canali, ma non può rimuovere la riverberazione a canale singolo;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho: Rimuove gli effetti di ritardo. Aggressive è più completo di Normal nella rimozione, DeReverb rimuove ulteriormente la riverberazione, può rimuovere la riverberazione a canale singolo, ma non rimuove completamente la riverberazione a piastra ad alta frequenza.", "*实验/模型名": "*Nome dell'esperimento/modello", "*文本标注文件": "*File di annotazione del testo", "*训练集音频文件目录": "*Directory dei file audio del set di addestramento", "*请上传并填写参考信息": "*Carica e compila le informazioni di riferimento", "*请填写需要合成的目标文本和语种模式": "*Si prega di inserire il testo di destinazione da sintetizzare e la modalità lingua", ".限制范围越小判别效果越好。": "Meno multilingue è meglio", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. Il tempo di elaborazione del modello DeEcho-DeReverb è quasi il doppio di quello degli altri due modelli DeEcho;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Conserva la voce principale: scegli questa opzione per audio senza armonie, poiché conserva meglio la voce principale rispetto al modello HP5. Include due modelli integrati, HP2 e HP3. HP3 potrebbe far passare leggermente l'accompagnamento ma conserva meglio la voce principale rispetto a HP2;", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Voce modificata", "2、MDX-Net-Dereverb模型挺慢的；": "2. <PERSON> modello MDX-Net-Dereverb è piuttosto lento;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. Solo conserva la voce principale: scegli questa opzione per audio con armonie, poiché potrebbe indebolire la voce principale. Include un modello HP5;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. La configurazione più pulita consigliata è MDX-Net seguito da DeEcho-Aggressive.", "3、去混响、去延迟模型（by FoxJoy）：": "3. <PERSON><PERSON> per rimuovere la riverberazione e il ritardo (by <PERSON><PERSON><PERSON>):", "ASR 模型": "Modello ASR", "ASR 模型尺寸": "Dimensioni del modello ASR", "ASR 语言设置": "Impostazioni linguistiche ASR", "CPU训练,较慢": "Addestramento su CPU, più lento", "GPT 训练: 模型权重文件在 GPT_weights/": "Addestramento GPT: i pesi del modello sono in GPT_weights/", "GPT模型列表": "Elenco dei modelli GPT", "GPT训练": "Addestramento GPT", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "Parametri di campionamento di GPT (non troppo bassi quando non c'è testo di riferimento. Utilizzare i valori predefiniti in caso di incertezza):", "GPU卡号,只能填1个整数": "Numero della scheda grafica, può essere inserito solo un numero intero", "GPU卡号以-分割，每个卡号一个进程": "Numero di GPU separati da '-'; ogni numero corrisponde a un processo", "LoRA秩": "<PERSON><PERSON>", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "Addestramento SoVITS: i pesi del modello sono in SoVITS_weights/", "SoVITS模型列表": "Elenco dei modelli SoVITS", "SoVITS训练": "Addestramento SoVITS", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "Invia testo: Salva manualmente i contenuti di tutti i campi testuali della pagina corrente in memoria e su file (se non premi questo pulsante prima o dopo aver girato pagina oppure prima di uscire dalla pagina di annotazione, tornando indietro i dati saranno ripristinati e avrai lavorato invano).", "TTS推理WebUI": "Interfaccia Web per inferenza TTS", "UVR5人声伴奏分离&去混响去延迟工具": "Strumento UVR5 per separazione voce/accompagnamento & rimozione riverbero e latenza", "V3不支持无参考文本模式，请填写参考文本！": "V3 non supporta la modalità senza testo di riferimento! Inserisci il testo di riferimento!", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: Quanta proporzione dell'audio normalizzato deve essere miscelata", "batch_size": "Dimensione del batch", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: Come calcolare la curva del volume. <PERSON><PERSON> piccolo <PERSON>, maggiore è la precisione ma aumenta la complessità computazionale (non significa che una maggiore precisione dà risultati migliori)", "max:归一化后最大值多少": "max: <PERSON><PERSON> valore dopo la normalizzazione", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: Massima durata del silenzio dopo il taglio", "min_interval:最短切割间隔": "min_interval: Intervallo minimo di taglio", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: <PERSON><PERSON><PERSON><PERSON> minima per segmento; se il primo segmento è troppo corto, sarà unito ai segmenti successivi fino a superare questo valore", "temperature": "temperatura", "threshold:音量小于这个值视作静音的备选切割点": "threshold: Punto di taglio alternativo considerato silenzioso se il volume è inferiore a questo valore", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "V3 non supporta questa modalità, utilizzarla genererà un errore.", "v3输出如果觉得闷可以试试开超分": "Se l'output v3 sembra ovattato, prova ad attivare il super-risoluzione", "不切": "Nessuna suddivisione", "不训练直接推v2ProPlus底模！": "Usa direttamente il modello base v2ProPlus senza addestramento!", "不训练直接推v2Pro底模！": "Usa direttamente il modello base v2Pro senza addestramento!", "不训练直接推v2底模！": "Usa direttamente il modello base v2 senza addestramento!", "不训练直接推v3底模！": "Usa direttamente il modello base v3 senza addestramento!", "中文": "Cinese", "中英混合": "Cinese e inglese misti", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "Audio di riferimento principale (Si prega di caricare un audio di riferimento tra 3 e 10 secondi, superato questo limite si verificherà un errore!)", "主参考音频的文本": "Testo dell'audio di riferimento principale", "主参考音频的语种": "Lingua dell'audio di riferimento principale", "也可批量输入音频文件, 二选一, 优先读文件夹": "È possibile anche inserire file audio in batch, una delle due opzioni, con priorità alla lettura della cartella", "人声伴奏分离批量处理， 使用UVR5模型。": "Separazione voce-accompagnamento in batch, utilizza il modello UVR5.", "人声分离WebUI": "Interfaccia Web per separazione vocale", "人声提取激进程度": "Grado di aggressività dell'estrazione vocale", "以下文件或文件夹不存在": "Nessun File o Cartella di Questo Tipo", "以下模型不存在:": "<PERSON><PERSON><PERSON> del Genere:", "伴奏人声分离&去混响&去回声": "Separazione tra accompagnamento e voce & Rimozione dell'eco & Rimozione dell'eco", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "Non hai scaricato i parametri del modello di super-risoluzione, quindi l'upscaling non verrà eseguito. Segui il tutorial per scaricare i file necessari.", "使用无参考文本模式时建议使用微调的GPT": "Si consiglia di usare un GPT fine-tuned quando si usa la modalità senza testo di riferimento.", "保存频率save_every_epoch": "Frequenza di salvataggio ogni epoca", "保持随机": "Mantieni casuale", "关闭": "<PERSON><PERSON> ", "凑50字一切": "Riempire con 50 caratteri per tutto", "凑四句一切": "Riempire con quattro frasi per tutto", "分桶处理模式已关闭": "Modalità di elaborazione per bucket disabilitata", "分桶处理模式已开启": "Modalità di elaborazione per bucket abilitata", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "La modalità di ritorno segmentato non supporta l'elaborazione per bucket, l'elaborazione per bucket è stata disabilitata automaticamente", "分段返回模式已开启": "Modalità di ritorno segmentato abilitata", "分段间隔(秒)": "Intervallo di segmentazione (secondi)", "分段间隔过小，已自动设置为0.01": "L'intervallo di segmentazione è troppo breve, impostato automaticamente a 0.01", "切分": "Segmentazione", "切分后文本": "Testo dopo segmentazione", "切分后的子音频的输出根目录": "Directory radice di output per gli audio segmentati", "切分文本": "Segmenta il testo", "切割使用的进程数": "Numero di processi utilizzati per il taglio", "刷新模型路径": "Aggiorna il percorso del modello", "前端处理后的文本(每句):": "<PERSON>o elaborato dal front-end (per frase):", "前置数据集获取工具": "Strumento per acquisizione dataset", "占用中": " Occupato", "去混响/去延迟，附：": "Rimozione della riverberazione/ritardo, allegato:", "参考音频在3~10秒范围外，请更换！": "L'audio di riferimento è al di fuori dell'intervallo di 3-10 secondi. Si prega di cambiarlo!", "参考音频的文本": "Testo dell'audio di riferimento", "参考音频的语种": "Lingua dell'audio di riferimento", "句间停顿秒数": "Durata pausa tra le frasi (secondi)", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Opzionale: Carica più file audio di riferimento trascinandoli (si consiglia dello stesso genere) e media il loro tono. Se questa opzione è lasciata vuota, il tono sarà controllato dal singolo file audio di riferimento a sinistra. Se si sta perfezionando il modello, è consigliato che tutti i file audio di riferimento abbiano toni presenti nel set di addestramento per il perfezionamento; il modello pre-addestrato può essere ignorato.", "合成语音": "Sintesi vocale", "合成音频": "Sintesi audio", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Formato di percorso della cartella valido: E:\\codes\\py39\\vits_vc_gpu\\Esempio di test di BaiLuShuangHua (copiare direttamente dalla barra degli indirizzi del gestore file).", "后续将支持转音素、手工修改音素、语音合成分步执行。": "In futuro verrà aggiunto il supporto per la conversione dei fonemi, la modifica manuale dei fonemi e la sintesi vocale passo dopo passo.", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "Se l'audio di riferimento non è chiaro o non sai cosa scrivere, abilita questa opzione per ignorare il testo di riferimento.", "启用并行推理版本": "Abilita versione di inferenza parallela", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Inserisci la directory dell'audio segmentato! Il percorso completo del file audio letto = questa directory - unione del nome del file corrispondente alle forme d'onda nel file .list (non il percorso completo). Se lasciato vuoto, verrà utilizzato il percorso assoluto nel file .list.", "多语种混合": "Mix multilingue", "多语种混合(粤语)": "<PERSON><PERSON> (Cantonese)", "失败": " Fallito", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "Se non accetti questi termini, non puoi utilizzare né citare alcun codice o file del pacchetto software. Vedi LICENSE nella directory principale.", "实际输入的参考文本:": "Testo di riferimento effettivamente inserito:", "实际输入的目标文本(切句后):": "Testo di destinazione effettivamente inserito (dopo il taglio delle frasi):", "实际输入的目标文本(每句):": "Testo di destinazione effettivamente inserito (per frase):", "实际输入的目标文本:": "Testo di destinazione effettivamente inserito:", "导出文件格式": "Formato di esportazione del file", "已关闭": " <PERSON><PERSON><PERSON>", "已完成": " Completato", "已开启": " <PERSON><PERSON><PERSON><PERSON>", "并行合成中": "Sin<PERSON><PERSON> parallela in corso", "并行推理": "Inferenza parallela", "并行推理模式已关闭": "Modalità di inferenza parallela disabilitata", "并行推理模式已开启": "Modalità di inferenza parallela abilitata", "底模缺失，无法加载相应 LoRA 权重": "Mancano il modello base, non è possibile caricare i pesi LoRA corrispondenti", "开启": "<PERSON><PERSON><PERSON> ", "开启无参考文本模式。不填参考文本亦相当于开启。": "Attivare la modalità senza testo di riferimento. Anche se non inserisci un testo di riferimento, la modalità verrà attivata.", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "Quando la modalità di inferenza parallela è attiva, i modelli SoVITS V3/4 non supportano l'elaborazione suddivisa in gruppi e questa è stata automaticamente disattivata.", "微调模型信息": "Informazioni sul modello raffinato", "微调训练": "Addestramento fine-tuning", "怎么切": "Come tagliare", "总训练轮数total_epoch": "Numero totale di epoche di addestramento", "总训练轮数total_epoch，不建议太高": "Numero totale di epoche di addestramento, non raccomandato troppo alto", "指定输出主人声文件夹": "Specifica la cartella di output per la voce principale", "指定输出非主人声文件夹": "Specifica la cartella di output per la non voce principale", "按中文句号。切": "Taglia secondo il punto cinese.", "按标点符号切": "Taglia secondo i segni di punteggiatura", "按英文句号.切": "Taglia secondo il punto inglese", "推理": "Inferenza", "推理设置": "Impostazioni di inferenza", "提取文本Bert特征": "Estrai caratteristiche Bert <PERSON>l testo", "数据分桶(并行推理时会降低一点计算量)": "Raggruppamento dei dati (Riduce il costo computazionale nell'inferenza parallela)", "数据类型精度": "precisione del tipo di dati", "文本分词与特征提取": "Tokenizzazione ed estrazione delle caratteristiche del testo", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "Strumento di segmentazione del testo. Testi troppo lunghi potrebbero non dare buoni risultati, quindi si consiglia di segmentarli. La sintesi verrà eseguita separando il testo in base ai ritorni a capo e poi concatenandolo.", "文本模块学习率权重": "Peso del tasso di apprendimento del modulo di testo", "施工中，请静候佳音": "In costruzione, attendi pazientemente le buone notizie", "日文": "Giapponese", "日英混合": "Mix giapponese e inglese", "是否仅保存最新的权重文件以节省硬盘空间": "<PERSON><PERSON><PERSON> solo i file di pesi più recenti per risparmiare spazio su disco?", "是否在每次保存时间点将最终小模型保存至weights文件夹": "<PERSON><PERSON><PERSON> il modello finale più piccolo nella cartella weights ad ogni punto di salvataggio", "是否开启DPO训练选项(实验性)": "Attivare l'opzione di addestramento DPO (sperimentale)?", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Regola la velocità del parlato e il tono dell'ultimo risultato di sintesi per prevenire la casualità.", "显卡信息": "Informazioni sulla scheda grafica", "未下载模型": "Modello non scaricato", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "Questo software è open-source sotto licenza MIT. L'autore non esercita alcun controllo sul software. L'utente e chiunque diffonda gli output audio generati sono pienamente responsabili.", "标注文件路径 (含文件后缀 *.list)": "Percorso del file di annotazione (con estensione *.list)", "模型": "<PERSON><PERSON>", "模型分为三类：": "I modelli sono divisi in tre categorie:", "模型切换": "Cambio del modello", "模型加载中，请等待": "Il modello si sta caricando, attendere prego...", "每张显卡的batch_size": "Batch size per ogni scheda grafica", "粤英混合": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "粤语": "Cantonese", "终止合成": "<PERSON><PERSON><PERSON>i", "缺少Hubert数据集": "Dataset di Hubert <PERSON>", "缺少语义数据集": "Dataset Semantico Mancante", "缺少音素数据集": "Dataset di Fonemi Mancante", "缺少音频数据集": "Dataset Audio Mancante", "英文": "<PERSON><PERSON><PERSON>", "训练模型的版本": "Versione del modello addestrato", "训练集格式化一键三连": "Formattazione del dataset di addestramento in un clic", "训练集格式化工具": "Strumento di formattazione dataset", "语义Token提取": "Estrazione token semantici", "语速": "Velocità della voce", "语速调整，高为更快": "Regolare la velocità della voce, più alta per più veloce", "语速调节不支持分桶处理，已自动关闭分桶处理": "Il controllo della velocità del discorso non supporta l'elaborazione per bucket, l'elaborazione per bucket è stata disabilitata automaticamente", "语音切分": "Segmentazione vocale", "语音切分工具": "Strumento di segmentazione vocale", "语音文本校对标注工具": "Strumento di annotazione e correzione testi vocali", "语音自监督特征提取": "Estrazione auto-supervisionata delle caratteristiche audio", "语音识别": "Riconoscimento vocale", "语音识别工具": "Strumento di riconoscimento vocale", "语音降噪": "Riduzione del rumore audio", "请上传3~10秒内参考音频，超过会报错！": "Carica un audio di riferimento della durata compresa tra 3 e 10 secondi. Superiore a questo, verrà generato un errore!", "请上传参考音频": "Si prega di caricare l'audio di riferimento", "请填入推理文本": "Si prega di inserire il testo di destinazione", "请填入正确的List路径": "Si Prega di Inserire il Percorso Corretto della Lista", "请填入正确的音频文件夹路径": "Si Prega di Inserire il Percorso Corretto della Cartella Audio", "请输入有效文本": "Inserisci un testo valido", "路径不存在,使用默认配置": "Percorso non trovato, utilizzo della configurazione predefinita", "路径不能为空": "Percorso Vuoto Non Consentito", "路径错误": "Errore di Percorso", "转换": "<PERSON><PERSON><PERSON>", "辅参考音频(可选多个，或不选)": "Audio di riferimento secondario (Facoltativo, seleziona più o nessuno)", "输入待处理音频文件夹路径": "Inserisci il percorso della cartella dei file audio da elaborare", "输入文件夹路径": "Inserisci il percorso della cartella", "输入路径不存在": "Il percorso di input non esiste", "输入路径存在但不可用": "Il percorso di input esiste ma non è utilizzabile", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Nella cartella logs/nome dell'esperimento dovrebbero esserci file e cartelle che iniziano con 23456", "输出信息": "Informazioni di output", "输出文件夹路径": "Percorso della cartella di output", "输出的语音": "Audio di output", "运行中": " In esecuzione", "进度": "Avanzamento", "进程已终止": " Processo terminato", "进程输出信息": " Output del processo", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "Seleziona i modelli addestrati salvati in SoVITS_weights e GPT_weights. Quelli predefiniti sono modelli base per sperimentare l'inferenza Zero Shot TTS in 5 secondi senza addestramento.", "采样步数(仅对V3/4生效)": "Numero di passaggi di campionamento (valido solo per V3/4)", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "Passi di campionamento: se sembra rumoroso, prova a aumentarlo, se è lento, prova a diminuirlo", "重复惩罚": "Penalità di ripetizione", "随机种子": "Seme casuale", "需先终止才能开启下一次任务": "Devi prima terminare il processo prima di avviare una nuova attività", "需要合成的切分前文本": "Testo prima della segmentazione per la sintesi", "需要合成的文本": "<PERSON>o da sintetizzare", "需要合成的文本的语种": "Lingua del testo da sintetizzare", "需要合成的语种": "Lingua da sintetizzare", "韩文": "<PERSON><PERSON>", "韩英混合": "<PERSON><PERSON>", "音频加载失败": "Caricamento Audio Fallito", "音频文件不存在，跳过：": "File audio non trovato, salto: ", "音频标注WebUI": "Interfaccia Web per annotazione audio", "音频自动切分输入路径，可文件可文件夹": "Percorso di input per la segmentazione automatica dell'audio, può essere un file o una cartella", "音频超分中": "Super-risoluzione audio in corso", "音频超采样": "Upsampling audio", "音频超采样(仅对V3生效))": "Upsampling audio (valido solo per V3)", "预测语义Token": "Predici token semantico", "预训练GPT模型路径": "Percorso del modello GPT pre-addestrato", "预训练SSL模型路径": "Percorso del modello SSL pre-addestrato", "预训练SoVITS-D模型路径": "Percorso del modello SoVITS-D pre-addestrato", "预训练SoVITS-G模型路径": "Percorso del modello SoVITS-G pre-addestrato", "预训练中文BERT模型路径": "Percorso del modello BERT cinese pre-addestrato", "预训练模型路径": "Percorso del modello pre-addestrato"}