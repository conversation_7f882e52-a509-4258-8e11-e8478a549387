{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb):İki kanallı yankılar için en iyi seçimdir, ancak tek kanallı yankıları ortadan kaldıramaz;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:<PERSON><PERSON>ik<PERSON> etki<PERSON>ini giderir. Aggressive, Normal'dan daha kapsamlı bir <PERSON><PERSON><PERSON> gider<PERSON>, DeReverb ek olarak yankıyı giderir, tek kanallı yankıyı giderebilir, ancak yüksek frekanslı plaka yankısını tamamen gideremez.", "*实验/模型名": "*Deney/model adı", "*文本标注文件": "*<PERSON><PERSON> et<PERSON><PERSON>ı", "*训练集音频文件目录": "*Eğitim seti ses dosyası dizini", "*请上传并填写参考信息": "*Lütfen referans bilgilerini yükleyin ve doldurun", "*请填写需要合成的目标文本和语种模式": "*Lütfen sentezlenecek hedef metni ve dil modunu giriniz.", ".限制范围越小判别效果越好。": "Daha az çok dilli olmak daha iyidir", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. De<PERSON>cho-DeReverb modelinin i<PERSON>lem<PERSON>, di<PERSON><PERSON> iki DeEcho modelinin neredeyse iki katıdır;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Se<PERSON> koruma: Arka vokal içermeyen sesler için bu seçeneği kullanın, ana sesi HP5'ten daha iyi korur. HP2 ve HP3 adlı iki model içerir; HP3, arka vokali biraz kaçırabilir ancak ana sesi HP2'ye göre biraz daha iyi korur;", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Ses Değiştirme", "2、MDX-Net-Dereverb模型挺慢的；": "2. MDX-Net-Dereverb modeli oldukça ya<PERSON>ştır;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. <PERSON><PERSON><PERSON> ana sesi koruma: Arka vokalleri içeren sesler için bu seçeneği kullanın, ana sesi zayıflatabilir. İçinde HP5 modeli var;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. <PERSON><PERSON><PERSON><PERSON> en temiz konfigürasyon MDX-Net'in ardından DeEcho-Aggressive'dir.", "3、去混响、去延迟模型（by FoxJoy）：": "3. <PERSON><PERSON><PERSON> <PERSON> gec<PERSON> gider<PERSON> modeli (FoxJoy tarafından):", "ASR 模型": "ASR modeli", "ASR 模型尺寸": "ASR model boy<PERSON><PERSON>", "ASR 语言设置": "ASR dil ayarları", "CPU训练,较慢": "CPU ile Eğ<PERSON>m, daha ya<PERSON>", "GPT 训练: 模型权重文件在 GPT_weights/": "GPT Eğitimi: Model ağırl<PERSON>k dosyaları GPT_weights/ içinde", "GPT模型列表": "GPT model listesi", "GPT训练": "GPT Eğitimi", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "GPT örnekleme parametreleri (referans metin olmadığında çok düşük olmamalıdır. <PERSON><PERSON> <PERSON>sayılanı kullanın):", "GPU卡号,只能填1个整数": "GPU kart numarası, sadece bir tamsayı girilebilir", "GPU卡号以-分割，每个卡号一个进程": "GPU kart numaralar<PERSON> - <PERSON><PERSON>, her kart numarası için bir işlem", "LoRA秩": "LoRA Derecesi", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "SoVITS Eğitimi: Model ağırl<PERSON>k dosyaları SoVITS_weights/ içinde", "SoVITS模型列表": "SoVITS model listesi", "SoVITS训练": "SoVITS Eğitimi", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "Geçerli sayfadaki tüm metin kutusu içeriklerini manuel olarak belleğe ve dosyaya kaydedin. Bu butona sayfa değiştirmeden önce ya da etiketleme sayfasından çıkmadan önce tıklamazsanız, geri döndüğünüzde değişiklikler geri alınıp tüm işlemler boşa gidecektir.", "TTS推理WebUI": "TTS Çıkarım WebUI", "UVR5人声伴奏分离&去混响去延迟工具": "UVR5 İnsan Sesli ve Enstrümantal Ayrım & Reverb ve Gecikme Giderme Aracı", "V3不支持无参考文本模式，请填写参考文本！": "V3, referans metin olmadan ç<PERSON>ışmayı desteklememektedir! Lütfen bir referans metin giriniz!", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix:Normalizasyondan sonraki sesin ne kadarlık bir oranı karıştırılsın", "batch_size": "Toplu Boyut", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size:Ses seviyesi eğrisi nasıl he<PERSON>, ne kadar küçükse hassasiyet o kadar yüksek ve hesaplama yükü o kadar artar (hassasiyet arttıkça etki mutlaka daha iyi olmaz)", "max:归一化后最大值多少": "max:<PERSON><PERSON><PERSON><PERSON><PERSON> sonra maksimum de<PERSON> ne kadar", "max_sil_kept:切完后静音最多留多长": "max_sil_kept:Kesimden sonra en fazla ne kadar sessizlik bırakılır", "min_interval:最短切割间隔": "min_interval:Minimum kesim aralığı", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, ilk bölüm çok kısa ise, bu de<PERSON><PERSON> a<PERSON>ana kadar sonraki böl<PERSON><PERSON>le birleştirilir", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "threshold:<PERSON><PERSON> bu değerden düşükse sessiz olarak kabul edilen alternatif kesim noktası", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "v3 bu modu des<PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nda hata verecektir.", "v3输出如果觉得闷可以试试开超分": "v3 çıkı<PERSON><PERSON> eğ<PERSON> s<PERSON><PERSON>, s<PERSON><PERSON>özünürlük açmayı deneyebilirsiniz", "不切": "<PERSON><PERSON><PERSON>", "不训练直接推v2ProPlus底模！": "Eğitim yapmadan doğrudan v2ProPlus temel modelini kullan!", "不训练直接推v2Pro底模！": "Eğitim yapmadan doğrudan v2Pro temel modelini kullan!", "不训练直接推v2底模！": "Eğitim yapmadan doğrudan v2 temel modelini kullan!", "不训练直接推v3底模！": "Eğitim yapmadan doğrudan v3 temel modelini kullan!", "中文": "<PERSON><PERSON><PERSON>", "中英混合": "Çince ve İngilizce karışık", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "Ana Referans Sesi (Lütfen 3-10 saniye arasında referans sesi yü<PERSON>, bu sınırı aşarsanız hata alırsınız!)", "主参考音频的文本": "Ana Referans <PERSON><PERSON>", "主参考音频的语种": "Ana Referans <PERSON>", "也可批量输入音频文件, 二选一, 优先读文件夹": "Ses dosyaları ayrıca toplu olarak, iki <PERSON>, öncelikli okuma klasörüyle içe aktarılabilir", "人声伴奏分离批量处理， 使用UVR5模型。": "Vokal ve akor ayırma toplu işleme, UVR5 modelini kullanarak.", "人声分离WebUI": "İnsan Sesi Ayrım WebUI", "人声提取激进程度": "Vokal çıkarma agresiflik derecesi", "以下文件或文件夹不存在": "<PERSON><PERSON><PERSON> veya Klasör Yo<PERSON>", "以下模型不存在:": "Böyle bir model yok:", "伴奏人声分离&去混响&去回声": "Vokal/Müzik Ayrıştırma ve Yankı Giderme", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "<PERSON><PERSON><PERSON> çözünürlük model parametrelerini indirmediniz, bu yüzden süper çözünürlük yapılmaz. Süper çözünürlük yapmak için önce kılavuzu takip ederek dosyaları indirin.", "使用无参考文本模式时建议使用微调的GPT": "Referans metinsiz modda, ince ayar yapılmış GPT kullanmanız tavsiye edilir.", "保存频率save_every_epoch": "<PERSON><PERSON><PERSON> sıklığı save_every_epoch", "保持随机": "Rastgeleliğ<PERSON>", "关闭": "Ka<PERSON><PERSON> ", "凑50字一切": "50 kelime bi<PERSON> ve kes", "凑四句一切": "<PERSON><PERSON><PERSON> c<PERSON>mleyi bir araya getirip kes", "分桶处理模式已关闭": "Kovaya İşleme Modu Kapalı", "分桶处理模式已开启": "Kovaya İşleme Modu <PERSON>kin", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "Bölümlü Dönüş Modu Kovaya İşlemeyi Desteklemediği İçin Kovaya İşleme Otomatik Olarak Kapatılmıştır", "分段返回模式已开启": "Bölümlü Dönüş <PERSON>", "分段间隔(秒)": "Bölüm Aralığı (Saniye)", "分段间隔过小，已自动设置为0.01": "Bölüm Aralığı Çok Kısa, Otomatik Olarak 0.01 Olarak Ayarlanmıştır", "切分": "Bölümleme", "切分后文本": "B<PERSON>lü<PERSON>lenmiş Metin", "切分后的子音频的输出根目录": "Bölündükten sonra alt ses dosyalarının çıktı kök dizini", "切分文本": "<PERSON><PERSON>", "切割使用的进程数": "Kesim için kullanılan işlem sayısı", "刷新模型路径": "Model yolu yenile", "前端处理后的文本(每句):": "<PERSON><PERSON> <PERSON> tabi tutulan metin (her cüm<PERSON>):", "前置数据集获取工具": "Ön Veri Kümesi Alma Aracı", "占用中": " Kullanımda", "去混响/去延迟，附：": "Yankı giderme/Geçikme giderme, ek:", "参考音频在3~10秒范围外，请更换！": "Referans ses dosyası 3~10 saniye aralığının dışı<PERSON>, lütfen değiştirin!", "参考音频的文本": "Referans ses dos<PERSON><PERSON><PERSON><PERSON>n metni", "参考音频的语种": "Referans ses dos<PERSON>ı<PERSON>ın dili", "句间停顿秒数": "Cümleler Arası Duraklama Süresi", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "İsteğe bağlı: Birden fazla referans ses dosyasını sürükleyip bırakarak yükleyin (aynı cinsiyetten olmaları önerilir) ve tonlarını ortalayın. Bu seçenek boş bırak<PERSON><PERSON><PERSON>, ton soldaki tek referans ses dosyası tarafından kontrol edilir. Modeli ince ayar yapıyorsanız, tüm referans ses dosyalarının ince ayar eğitim seti içindeki tonlara sahip olması önerilir; önceden eğitilmiş model dikkate alınmayabilir.", "合成语音": "<PERSON><PERSON> sentezi", "合成音频": "<PERSON><PERSON>", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Geçerli klasör yolu formatı örneği: E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (dosya yöneticisi adres çubuğundan kopyalayabilirsiniz).", "后续将支持转音素、手工修改音素、语音合成分步执行。": "<PERSON><PERSON>i sürümlerde fonem dönüşümü, el ile fonem düzenleme ve adım adım konuşma sentezi desteği eklenecek.", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "Referans ses kaydını anlamıyorsanız (ne yazacağınızı bilmiyorsanız) açabilirsiniz. Açıldığında yazılmış olan referans metni göz ardı edilir.", "启用并行推理版本": "Paralel Çıkarım Versiyonunu Etkinleştir", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Kesmeye uygun ses dosyalarının bulunduğu dizini doldurun! Okunan ses dosyasının tam yolu = bu dizin + list dosyasındaki dalga biçimiyle eşleşen dosya adı (tam yol değil). Boş bırakılırsa, .list dosyasındaki tam yol kullanılır.", "多语种混合": "Çok dilli karışım", "多语种混合(粤语)": "Çok dilli karışık (Yue)", "失败": " Başarısız", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "Bu şartları kabul etmiyorsanız, yaz<PERSON><PERSON><PERSON><PERSON> paketindeki herhangi bir kodu veya dosyayı kullanamaz veya alıntılayamazsınız. Detaylar için ana dizindeki LICENSE dosyasına bakın.", "实际输入的参考文本:": "Gerçekten girilen referans metin:", "实际输入的目标文本(切句后):": "Gerçekten girilen hedef metin (cümleler kesildikten sonra):", "实际输入的目标文本(每句):": "Gerçekten girilen hedef metin (her c<PERSON><PERSON><PERSON>):", "实际输入的目标文本:": "Gerçekten girilen hedef metin:", "导出文件格式": "Dışa aktarma dosya formatı", "已关闭": " <PERSON><PERSON><PERSON>", "已完成": " Tamamlandı", "已开启": " Açık", "并行合成中": "<PERSON><PERSON>l Sen<PERSON>zleme Ya<PERSON>ılıyor", "并行推理": "Paralel <PERSON>ıkarım", "并行推理模式已关闭": "Paralel Çıkarım Modu Kapalı", "并行推理模式已开启": "Paralel Çıkarım Modu Etkin", "底模缺失，无法加载相应 LoRA 权重": "Temel model eks<PERSON>, il<PERSON>li Lo<PERSON> ağırlıkları yüklenemedi.", "开启": "Aç ", "开启无参考文本模式。不填参考文本亦相当于开启。": "Referans metni olmayan mod açık. Referans metni doldurulmazsa bu mod otomatik olarak açılır.", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "Paralel çıkarım modu etkinleştirildiğinde, SoVITS V3/4 modelleri bölme işlemeyi desteklemez ve bu işlem otomatik olarak devre dışı bırakılır.", "微调模型信息": "İnce Ayar Model Bilgisi", "微调训练": "İnce Ayar Eğitimi", "怎么切": "<PERSON><PERSON><PERSON><PERSON> kesi<PERSON>", "总训练轮数total_epoch": "Toplam eğitim turu sayısı total_epoch", "总训练轮数total_epoch，不建议太高": "Toplam eğitim turu sayısı total_epoch, çok yüksek önerilmez", "指定输出主人声文件夹": "Vokal için çı<PERSON> klasörünü belirtin:", "指定输出非主人声文件夹": "Müzik ve diğer sesler için çıkış klasörünü belirtin:", "按中文句号。切": "<PERSON><PERSON><PERSON> dönem işaretine göre kes", "按标点符号切": "Noktalama işaretlerine göre kes", "按英文句号.切": "İngilizce nokta işaretine göre kes", "推理": "Çıkarım", "推理设置": "Çıkarım Ayarları", "提取文本Bert特征": "Metin BERT Özelliklerini Çıkar", "数据分桶(并行推理时会降低一点计算量)": "Veri Kovaya Ayrılması (Paralel Çıkarımda Hesaplama Maliyetini Azaltır)", "数据类型精度": "veri tü<PERSON><PERSON> doğruluğ<PERSON>", "文本分词与特征提取": "<PERSON><PERSON> ve Özellik Çıkartma", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "<PERSON>in <PERSON>lümleme Ara<PERSON>ı. Çok uzun metinlerin sentez sonuçları iyi olmayabilir, bu nedenle uzun metinleri önce bölümlere ayırmanız önerilir. <PERSON><PERSON><PERSON>, metnin satır sonlarına göre ayrı ayrı yapılıp sonra birleştirilecektir.", "文本模块学习率权重": "<PERSON>in modülü öğrenme oranı ağırlığı", "施工中，请静候佳音": "<PERSON><PERSON><PERSON><PERSON>, lütfen iyi haberler i<PERSON><PERSON> be<PERSON>", "日文": "Japonca", "日英混合": "Japonca ve İngilizce karışımı", "是否仅保存最新的权重文件以节省硬盘空间": "Sadece en son a<PERSON><PERSON><PERSON><PERSON><PERSON>ını kaydedip sabit disk alanı tasarrufu sağlamak isterseniz", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Her kayıt zamanında son k<PERSON><PERSON><PERSON><PERSON> modelin weights klasörüne kaydedilmesi gerekiyor mu", "是否开启DPO训练选项(实验性)": "DPO Eğitim Seçeneğini Açmak (Deneysel)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Rastgeleliği önlemek için son sentez sonucunun konuşma hızını ve tonunu ayarlayın.", "显卡信息": "Ekran kartı bilgisi", "未下载模型": "<PERSON> <PERSON><PERSON><PERSON>", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "Bu yazılım MIT lisansı ile açık kaynak olarak sunulmuştur, yazar yazılım üzerinde herhangi bir kontrol sahibi değ<PERSON>dir, yazılımı kullanan veya yazılımın çıktısını dağıtan kişiler tüm sorumluluğu üstlenir.", "标注文件路径 (含文件后缀 *.list)": "Etiketleme Dosyası Yolu (dosya uzantısı *.list dahil)", "模型": "Model", "模型分为三类：": "Modeller üç türdedir:", "模型切换": "<PERSON> <PERSON><PERSON><PERSON>", "模型加载中，请等待": "Model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...", "每张显卡的batch_size": "Her bir ekran kartı için batch_size", "粤英混合": "Yue-İngilizce Karışık", "粤语": "<PERSON><PERSON>", "终止合成": "<PERSON><PERSON><PERSON>", "缺少Hubert数据集": "<PERSON>", "缺少语义数据集": "Anlamsal Veri Seti Eksik", "缺少音素数据集": "<PERSON>onem Veri Seti Eksik", "缺少音频数据集": "Ses Veri Seti E<PERSON>", "英文": "İngilizce", "训练模型的版本": "Eğitilmiş Model Versiyonu", "训练集格式化一键三连": "Eğitim Seti Formatlama Tek Tıkla Üçlü", "训练集格式化工具": "Eğitim Seti Formatlama Aracı", "语义Token提取": "Anlamlı Token Çıkartma", "语速": "Konuşma hızı", "语速调整，高为更快": "Konuşma hızını a<PERSON>, y<PERSON><PERSON><PERSON> daha hızlı", "语速调节不支持分桶处理，已自动关闭分桶处理": "Konuşma Hızı Ayarlaması Kovaya İşlemeyi Desteklemediği İçin Kovaya İşleme Otomatik Olarak Kapatılmıştır", "语音切分": "<PERSON><PERSON>", "语音切分工具": "<PERSON><PERSON>", "语音文本校对标注工具": "Ses Metni Düzeltme Etiketleme Aracı", "语音自监督特征提取": "Ses Kendiliğinden Denetimli Özellik Çıkartma", "语音识别": "<PERSON><PERSON>", "语音识别工具": "<PERSON><PERSON>", "语音降噪": "Ses Gürültü Azaltma", "请上传3~10秒内参考音频，超过会报错！": "Lütfen 3~10 saniye arasında bir referans ses dosyası yü<PERSON>, aşım durumunda hata verilecektir!", "请上传参考音频": "Lütfen Referans Sesi Yükleyin", "请填入推理文本": "Lütfen Hedef <PERSON>", "请填入正确的List路径": "Lütfen Doğru Liste Yolunu Girin", "请填入正确的音频文件夹路径": "Lütfen Doğru Ses Klasörü Yolunu G<PERSON>n", "请输入有效文本": "Ge<PERSON><PERSON><PERSON> metin girin", "路径不存在,使用默认配置": "Yol Bulunamadı, Varsayılan Yapılandırma Kullanılıyor", "路径不能为空": "Boş Yol Beklenmiyor", "路径错误": "<PERSON>l <PERSON>", "转换": "Dönüş<PERSON>ür", "辅参考音频(可选多个，或不选)": "Yardımcı Referans Sesi (İsteğe bağlı birden çok seçilebilir veya hiç seçilmeyebilir)", "输入待处理音频文件夹路径": "İşlenecek ses klasörünün yolunu girin:", "输入文件夹路径": "<PERSON><PERSON><PERSON> yolu girin", "输入路径不存在": "<PERSON><PERSON><PERSON>ğil", "输入路径存在但不可用": "<PERSON><PERSON><PERSON> Mevcut Ama <PERSON>", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Çı<PERSON><PERSON> logs/deney adı dizininde 23456 ile başlayan dosya ve klasörler olmalı", "输出信息": "Çıkış bilgisi", "输出文件夹路径": "Çıktı klasörü yolu", "输出的语音": "Çı<PERSON><PERSON> sesi", "运行中": " Çalışıyor", "进度": "<PERSON><PERSON><PERSON><PERSON>", "进程已终止": " İşlem Sonlandırıldı", "进程输出信息": " İşlem Çıktı Bilgisi", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "SoVITS_weights ve GPT_weights altında kayıtlı eğitilmiş modelleri seçin. Varsayılanlar temel modellerdir, 5 saniyede Zero Shot TTS çıkarımını eğitimsiz deneyimleyin.", "采样步数(仅对V3/4生效)": "Örnekleme Adım <PERSON>ı (Sadece V3/4 için geçerli)", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "Örnekleme Adımları: <PERSON><PERSON><PERSON> gürültül<PERSON> gör<PERSON><PERSON><PERSON><PERSON><PERSON>, adı<PERSON><PERSON><PERSON> artırın; eğer yava<PERSON> görünüyo<PERSON>, adı<PERSON><PERSON><PERSON> azaltın", "重复惩罚": "Tekrarlama Cezası", "随机种子": "<PERSON><PERSON><PERSON><PERSON>", "需先终止才能开启下一次任务": "Bir sonraki görevi başlatmadan önce mevcut işlem sonlandırılmalıdır", "需要合成的切分前文本": "Sen<PERSON>z <PERSON>ö<PERSON>ümlenmemiş Metin", "需要合成的文本": "Sentezlenmesi gereken metin", "需要合成的文本的语种": "<PERSON><PERSON><PERSON>", "需要合成的语种": "Sentezlenmesi gereken dil", "韩文": "<PERSON><PERSON><PERSON>", "韩英混合": "Korece-İngilizce Karışık", "音频加载失败": "<PERSON><PERSON>", "音频文件不存在，跳过：": "Ses dosyası bulunamadı, atlanıyor: ", "音频标注WebUI": "Ses Etiketleme WebUI", "音频自动切分输入路径，可文件可文件夹": "<PERSON>s otomatik b<PERSON><PERSON><PERSON>, dosya veya klasör o<PERSON>bilir", "音频超分中": "Ses Süper Çözünürlük İşlemi Devam Ediyor", "音频超采样": "Ses Üst-örnekleme", "音频超采样(仅对V3生效))": "Ses Üst-örnekleme (Sadece V3 için geçerli)", "预测语义Token": "<PERSON><PERSON><PERSON>", "预训练GPT模型路径": "Önceden Eğitilmiş GPT Modeli Yolu", "预训练SSL模型路径": "Önceden Eğitilmiş SSL Modeli Yolu", "预训练SoVITS-D模型路径": "Önceden Eğitilmiş SoVITS-D Modeli Yolu", "预训练SoVITS-G模型路径": "Önceden Eğitilmiş SoVITS-G Modeli Yolu", "预训练中文BERT模型路径": "Önceden Eğitilmiş Çince BERT Modeli Yolu", "预训练模型路径": "Önceden Eğitilmiş Model Yolu"}