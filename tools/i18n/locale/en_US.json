{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb): Best choice for dual-channel reverberation, cannot remove single-channel reverberation;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho: Removes delay effects. Aggressive mode removes more thoroughly than Normal mode. DeReverb additionally removes reverberation, can remove mono reverberation, but does not clean heavily high-frequency plate reverberation.", "*实验/模型名": "*Experiment/model name", "*文本标注文件": "*Text labelling file", "*训练集音频文件目录": "*Audio dataset folder", "*请上传并填写参考信息": "*Please upload and fill reference information", "*请填写需要合成的目标文本和语种模式": "*Please fill in the target text and language mode for synthesis", ".限制范围越小判别效果越好。": "Less Multilingual is better", "1-GPT-SoVITS-TTS": "1-GPT-SOVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. The DeEcho-DeReverb model's processing time is nearly twice that of the other two DeEcho models.", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Preserve Vocals: Choose this option for audio without harmonies, as it better retains the main vocal compared to the HP5 model. This option includes two built-in models, HP2 and HP3. HP3 may slightly let through some accompaniment but retains the main vocal slightly better than HP2.", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Voice Changer", "2、MDX-Net-Dereverb模型挺慢的；": "2、MDX-Net-Dereverb Model is slow;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. Keep Only Main Vocal: Choose this option for audio with harmonies, as it may slightly reduce the main vocal. Includes one built-in HP5 model;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. Personal Recommendation for the cleanest configuration: First use MDX-Net followed by DeEcho-Aggressive", "3、去混响、去延迟模型（by FoxJoy）：": "3. Reverberation and delay removal model(by <PERSON><PERSON><PERSON>):", "ASR 模型": "ASR model", "ASR 模型尺寸": "ASR model size", "ASR 语言设置": "ASR language", "CPU训练,较慢": "Training on CPU (slower)", "GPT 训练: 模型权重文件在 GPT_weights/": "GPT Training: Model Weights saved in GPT_weights/", "GPT模型列表": "GPT weight list", "GPT训练": "GPT Training", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "GPT sampling parameters (not too low when there's no reference text. Use default if unsure):", "GPU卡号,只能填1个整数": "GPU number, can only input ONE integer", "GPU卡号以-分割，每个卡号一个进程": "GPU number is separated by -, each GPU will run one process ", "LoRA秩": "LoRA Rank", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "SoVITS Training: Model Weights saved in SoVITS_weights/", "SoVITS模型列表": "SoVITS weight list", "SoVITS训练": "SoVITS Training", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "Submit Text: Manually save all text box contents on the current page to memory and file (If you don't click this button before switching pages or exiting the labeling page, the data will be rolled back when you return, which would be a waste of work.)", "TTS推理WebUI": "TTS Inference WebUI", "UVR5人声伴奏分离&去混响去延迟工具": "UVR5 WebUI (Vocal Separation/Deecho/Dereverb)", "V3不支持无参考文本模式，请填写参考文本！": "V3 does not support the no-reference-text mode. Please provide reference text!", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: proportion of normalized audio merged into dataset", "batch_size": "<PERSON><PERSON> Si<PERSON>", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: FO hop size, the smaller the value, the higher the accuracy）", "max:归一化后最大值多少": "Loudness multiplier after normalized", "max_sil_kept:切完后静音最多留多长": "Maximum length for silence to be kept", "min_interval:最短切割间隔": "Minumum interval for audio cutting", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: the minimum length of each segment. If the first segment is too short, it will be concatenated with the next segment until it exceeds this value", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "Noise gate threshold (loudness below this value will be treated as noise", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "v3 does not support this mode currently, using it will cause an error.", "v3输出如果觉得闷可以试试开超分": "For V3 model, if generated audio sounds somewhat muffled, try enable audio super-resolution.", "不切": "No slice", "不训练直接推v2ProPlus底模！": "Use v2ProPlus base model directly without training!", "不训练直接推v2Pro底模！": "Use v2Pro base model directly without training!", "不训练直接推v2底模！": "Use v2 base model directly without training!", "不训练直接推v3底模！": "Use v3 base model directly without training!", "中文": "Chinese", "中英混合": "Chinese-English Mixed", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "Primary Reference Audio (Please upload reference audio within 3-10 seconds, exceeding this limit will cause an error!)", "主参考音频的文本": "Text of Primary Reference Audio", "主参考音频的语种": "Language of Primary Reference Audio", "也可批量输入音频文件, 二选一, 优先读文件夹": "Multiple audio files can also be imported. If a folder path exists, this input is ignored.", "人声伴奏分离批量处理， 使用UVR5模型。": "Batch processing for vocal and instrumental separation, using the UVR5 model.", "人声分离WebUI": "Vocal Separation WebUI", "人声提取激进程度": "Vocal extraction aggressiveness", "以下文件或文件夹不存在": "No Such File or Folder", "以下模型不存在:": "No Such Model:", "伴奏人声分离&去混响&去回声": "Vocals/Accompaniment Separation & Reverberation Removal", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "Super-Resolution Model Not Found. Please follow the tutorial to download the model file if you want to use it.", "使用无参考文本模式时建议使用微调的GPT": "Recommended to use a Finetune-GPT when using Prompt-Free Mode.", "保存频率save_every_epoch": "Save frequency (save_every_epoch):", "保持随机": "Keep Random", "关闭": "Close ", "凑50字一切": "Slice per 50 characters", "凑四句一切": "Slice once every 4 sentences", "分桶处理模式已关闭": "Bucket Processing Mode Disabled", "分桶处理模式已开启": "Bucket Processing Mode Enabled", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "Segmented Return Mode does not support Bucket Processing, Bucket Processing Disabled automatically", "分段返回模式已开启": "Segmented Return Mode Enabled", "分段间隔(秒)": "Segment Interval (Seconds)", "分段间隔过小，已自动设置为0.01": "Segment Interval too short, automatically set to 0.01", "切分": "Segmentation", "切分后文本": "Inference Text After Segmentation", "切分后的子音频的输出根目录": "Audio slicer output folder", "切分文本": "Segment Text", "切割使用的进程数": "CPU threads used for audio slicing", "刷新模型路径": "refreshing model paths", "前端处理后的文本(每句):": "Processed text from the frontend (per sentence):", "前置数据集获取工具": "Fetch Datasets", "占用中": " Occupying", "去混响/去延迟，附：": "Dereverberation/Delay Removal, including:", "参考音频在3~10秒范围外，请更换！": "Reference audio is outside the 3-10 second range, please choose another one!", "参考音频的文本": "Text for reference audio", "参考音频的语种": "Language for reference audio", "句间停顿秒数": "Pause Duration between Sentences (Seconds)", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Optional: Upload multiple reference audio files by dragging and dropping them (recommended to be of the same gender), and average their tone. If this option is left blank, the tone will be controlled by the single reference audio on the left. If fine-tuning the model, it is recommended that all reference audio files have tones within the fine-tuning training set; the pretrained model can be ignored.", "合成语音": "Start inference", "合成音频": "Synthesize Audio", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "An example of a valid folder path format: E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (simply copy the address from the file manager's address bar).", "后续将支持转音素、手工修改音素、语音合成分步执行。": "Support for Phoneme Conversion, Manual Phoneme Editing, and Step-by-Step Speech Synthesis will be added in the future.", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "If reference audio is not clear or unsure what to write, enable this option to ignore the reference text.", "启用并行推理版本": "Enable Parallel Inference Version", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Please fill in the segmented audio files' directory! The full path of the audio file = the directory concatenated with the filename corresponding to the waveform in the list file (not the full path). If left blank, the absolute full path in the .list file will be used.", "多语种混合": "Multilingual Mixed", "多语种混合(粤语)": "Multilingual Mixed(Yue)", "失败": " Failed", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "If you do not agree with this clause, you cannot use or reference any codes and files within the software package. See the root directory Agreement-LICENSE for details.", "实际输入的参考文本:": "Actual Input Reference Text:", "实际输入的目标文本(切句后):": "Actual Input Target Text (after sentence segmentation):", "实际输入的目标文本(每句):": "Actual Input Target Text (per sentence):", "实际输入的目标文本:": "Actual Input Target Text:", "导出文件格式": "Export file format", "已关闭": " is Closed", "已完成": " Finished", "已开启": " is Opened", "并行合成中": "Parallel Synthesis in Progress", "并行推理": "Parallel Inference", "并行推理模式已关闭": "Parallel Inference Mode Disabled", "并行推理模式已开启": "Parallel Inference Mode Enabled", "底模缺失，无法加载相应 LoRA 权重": "Missing Pretrained Model, Cannot Load LoRA Weights", "开启": "Open ", "开启无参考文本模式。不填参考文本亦相当于开启。": "Enable no reference mode. If you don't fill 'Text for reference audio', no reference mode will be enabled.", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "When parallel inference mode is enabled, SoVITS V3/4 models do not support bucket processing; bucket processing has been automatically disabled.", "微调模型信息": "Fine-tuned Model Information", "微调训练": "Fine-Tuning", "怎么切": "How to slice the sentence", "总训练轮数total_epoch": "Total training epochs (total_epoch):", "总训练轮数total_epoch，不建议太高": "Total epochs, do not increase to a value that is too high", "指定输出主人声文件夹": "Specify the output folder for vocals:", "指定输出非主人声文件夹": "Specify the output folder for accompaniment:", "按中文句号。切": "Slice by Chinese punct", "按标点符号切": "Slice by every punct", "按英文句号.切": "Slice by English punct", "推理": "Inference", "推理设置": "Inference Settings", "提取文本Bert特征": "Extract Text BERT Features", "数据分桶(并行推理时会降低一点计算量)": "Data Bucketing (Reduces Computation Cost in Parallel Inference)", "数据类型精度": "Computing precision", "文本分词与特征提取": "Tokenization & BERT Feature Extraction", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "Text Segmentation Tool. Very long text may not yield good synthesis results, so Segmentation is Recommended. Synthesis will be performed based on line breaks and then concatenated.", "文本模块学习率权重": "Text model learning rate weighting", "施工中，请静候佳音": "In construction, please wait", "日文": "Japanese", "日英混合": "Japanese-English Mixed", "是否仅保存最新的权重文件以节省硬盘空间": "Save only the latest weight file to save disk space", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Save a small final model to the 'weights' folder at each save point:", "是否开启DPO训练选项(实验性)": "Enable DPO Training (Experimental)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Adjust the speech rate and tone of the last synthesis result to prevent randomness.", "显卡信息": "GPU Information", "未下载模型": "Model Not Downloaded", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "This software is open source under the MIT license. The author does not have any control over the software. Users who use the software and distribute the sounds exported by the software are solely responsible.", "标注文件路径 (含文件后缀 *.list)": "Label File Path (with file extension *.list)", "模型": "Model", "模型分为三类：": "Models are categorized into three types:", "模型切换": "Model switch", "模型加载中，请等待": "Model is loading, please wait...", "每张显卡的batch_size": "Batch size per GPU:", "粤英混合": "<PERSON><PERSON>-English Mixed", "粤语": "<PERSON><PERSON>", "终止合成": "Terminate Synthesis", "缺少Hubert数据集": "Missing <PERSON>", "缺少语义数据集": "Missing Semantics Dataset", "缺少音素数据集": "Missing Phoneme Dataset", "缺少音频数据集": "Missing Audio Dataset", "英文": "English", "训练模型的版本": "Version of the trained model", "训练集格式化一键三连": "Training Set One-Click Formatting", "训练集格式化工具": "Dataset Formatting Tool", "语义Token提取": "Semantics Token Extraction", "语速": "Speech rate", "语速调整，高为更快": "Adjust speech rate, higher for faster", "语速调节不支持分桶处理，已自动关闭分桶处理": "Speech Rate Adjustment does not support Bucket Processing, Bucket Processing Disabled automatically", "语音切分": "Speech Slicing", "语音切分工具": "Speech Slicing Tool", "语音文本校对标注工具": "Speech-to-Text Proofreading Tool", "语音自监督特征提取": "Speech SSL Feature Extraction", "语音识别": "Speech Recognition", "语音识别工具": "Speech Recognition Tool", "语音降噪": "Speech Denoising", "请上传3~10秒内参考音频，超过会报错！": "Please upload a reference audio within the 3-10 second range; if it exceeds this duration, it will raise errors.", "请上传参考音频": "Please Upload the Reference Audio", "请填入推理文本": "Please Fill in the Terget Text", "请填入正确的List路径": "Please Fill in the Correct List Path", "请填入正确的音频文件夹路径": "Please Fill in the Correct Audio Folder Path", "请输入有效文本": "Please enter valid text.", "路径不存在,使用默认配置": "Paths Not Found, Using Default Config", "路径不能为空": "Expected No Empty Path", "路径错误": "Path Error", "转换": "Convert", "辅参考音频(可选多个，或不选)": "Secondary Reference Audio (Multiple Optional, or None)", "输入待处理音频文件夹路径": "Enter the path of the audio folder to be processed:", "输入文件夹路径": "Input folder path", "输入路径不存在": "Input Path Not Found", "输入路径存在但不可用": "Input Path Exists but Unavailable", "输出logs/实验名目录下应有23456开头的文件和文件夹": "output folder (logs/{experiment name}) should have files and folders starts with 23456.", "输出信息": "Output information", "输出文件夹路径": "Output folder path", "输出的语音": "Inference Result", "运行中": " Running", "进度": "Progress", "进程已终止": " Process Terminated", "进程输出信息": " Process Output Information", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "Select the model from SoVITS_weights and GPT_weights. The default models are pretrained models for experiencing 5-second Zero-Shot TTS without training.", "采样步数(仅对V3/4生效)": "Sampling Steps (V3/V4 Only)", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "Sampling Steps: If feel noisy, try increasing, if feel slow, try decreasing", "重复惩罚": "Repetition Penalty", "随机种子": "Random Seed", "需先终止才能开启下一次任务": "Please Terminate First to Start Next Task", "需要合成的切分前文本": "Inference Text Before Segmentation", "需要合成的文本": "Inference text", "需要合成的文本的语种": "Language of the Inference Text", "需要合成的语种": "Inference text language", "韩文": "Korean", "韩英混合": "Korean-English Mixed", "音频加载失败": "Failed to Load Audio", "音频文件不存在，跳过：": "Audio File Not Found, Skipping: ", "音频标注WebUI": "Audio Labeling WebUI", "音频自动切分输入路径，可文件可文件夹": "Audio slicer input (file or folder)", "音频超分中": "Running Audio Super-Resolution", "音频超采样": "Audio Upsampling", "音频超采样(仅对V3生效))": "Audio Upsampling (V3 Only)", "预测语义Token": "Predict <PERSON><PERSON><PERSON>", "预训练GPT模型路径": "Pretrained GPT Model Path", "预训练SSL模型路径": "Pretrained SSL Model Path", "预训练SoVITS-D模型路径": "Pretrained SoVITS-D Model Path", "预训练SoVITS-G模型路径": "Pretrained SoVITS-G Model Path", "预训练中文BERT模型路径": "Pretrained Chinese BERT Model Path", "预训练模型路径": "Pretrained Model Path"}