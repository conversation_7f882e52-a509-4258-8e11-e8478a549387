{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1) MDX-Net (onnx_dereverb) : C'est le meilleur choix pour la réverbération à deux canaux, mais il ne peut pas éliminer la réverbération à un seul canal;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho : Supprime les effets de délai. Aggressive est plus exhaustif que Normal dans la suppression, DeReverb élimine également la réverbération, peut supprimer la réverbération monocanal, mais n'élimine pas complètement la réverbération de plaque à haute fréquence.", "*实验/模型名": "*Nom de l'expérience/modèle", "*文本标注文件": "*Fichier d'annotation de texte", "*训练集音频文件目录": "*Répertoire des fichiers audio d'entraînement", "*请上传并填写参考信息": "*Veuillez télécharger et remplir les informations de référence", "*请填写需要合成的目标文本和语种模式": "*Veuillez saisir le texte cible à synthétiser et le mode de langue.", ".限制范围越小判别效果越好。": "Moins il y a de langues, mieux c'est", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. Le temps de traitement du modèle <PERSON>-DeReverb est presque le double de celui des deux autres modèles DeEcho;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Préserver les voix : Choisissez cette option pour les audio sans harmonie, car elle conserve mieux la voix principale par rapport au modèle HP5. Deux modèles intégrés, HP2 et HP3, sont disponibles. HP3 peut légèrement laisser passer l'accompagnement mais conserve la voix principale un peu mieux que HP2;", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Modification de la voix", "2、MDX-Net-Dereverb模型挺慢的；": "2. <PERSON> modèle MDX-Net-Dereverb est assez lent;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. Conserver uniquement la voix principale : Choisissez cette option pour les audio avec harmonie, car elle peut affaiblir la voix principale. Un modèle HP5 intégré est disponible;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. La configuration la plus propre que je recommande est d'utiliser d'abord MDX-Net, puis DeEcho-Aggressive.", "3、去混响、去延迟模型（by FoxJoy）：": "3. Modèle de suppression de réverbération et de retard (par FoxJoy) :", "ASR 模型": "Modèle ASR", "ASR 模型尺寸": "Taille du modèle ASR", "ASR 语言设置": "Paramètres de langue ASR", "CPU训练,较慢": "Entraînement sur CPU, plus lent", "GPT 训练: 模型权重文件在 GPT_weights/": "Entraînement GPT : les poids du modèle sont dans GPT_weights/", "GPT模型列表": "Liste des modèles GPT", "GPT训练": "Entraînement GPT", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "Paramètres d'échantillonnage de GPT (ne pas mettre trop bas lorsqu'il n'y a pas de texte de référence. Utilisez les valeurs par défaut si vous n'êtes pas sûr):", "GPU卡号,只能填1个整数": "Numéro de carte GPU, ne peut contenir qu'un seul entier", "GPU卡号以-分割，每个卡号一个进程": "Numéro de carte GPU séparé par des tirets, un processus par numéro de carte", "LoRA秩": "Rang Lo<PERSON>", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "Entraînement SoVITS : les poids du modèle sont dans SoVITS_weights/", "SoVITS模型列表": "Liste des modèles SoVITS", "SoVITS训练": "Entraînement SoVITS", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "Soumettre le texte : Enregistrez manuellement le contenu de tous les champs textuels de la page actuelle en mémoire et dans un fichier (si vous ne cliquez pas sur ce bouton avant ou après avoir changé de page, ou avant de quitter la page d'étiquetage, vos modifications seront annulées lorsque vous reviendrez, tout votre travail sera perdu).", "TTS推理WebUI": "Interface Web d'inférence TTS", "UVR5人声伴奏分离&去混响去延迟工具": "Outil UVR5 de séparation voix/accompagnement & suppression de réverbération et de latence", "V3不支持无参考文本模式，请填写参考文本！": "La version V3 ne prend pas en charge le mode sans texte de référence. Veuillez fournir un texte de référence !", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: proportion d'audio normalisé mélangé", "batch_size": "<PERSON><PERSON>", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: comment calculer la courbe de volume, plus petit pour une précision plus élevée mais une charge de calcul plus élevée (ce n'est pas une meilleure précision)", "max:归一化后最大值多少": "max: valeur maximale après normalisation", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: durée maximale de silence après la coupe", "min_interval:最短切割间隔": "min_interval: intervalle de coupe minimum", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length:longueur minimale de chaque segment ; si le premier segment est trop court, il est concaténé avec les segments suivants jusqu'à ce que la longueur dépasse cette valeur", "temperature": "température", "threshold:音量小于这个值视作静音的备选切割点": "seuil: le volume inférieur à cette valeur est considéré comme un point de coupe silencieux alternatif", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "Le mode n'est pas encore supporté par la v3. Une erreur se produira en cas d'utilisation.", "v3输出如果觉得闷可以试试开超分": "Si la sortie v3 semble étouffée, essayez l'upscaling", "不切": "Pas de découpe", "不训练直接推v2ProPlus底模！": "Utiliser directement le modèle de base v2ProPlus sans entraînement !", "不训练直接推v2Pro底模！": "Utiliser directement le modèle de base v2Pro sans entraînement !", "不训练直接推v2底模！": "Utiliser directement le modèle de base v2 sans entraînement !", "不训练直接推v3底模！": "Utiliser directement le modèle de base v3 sans entraînement !", "中文": "<PERSON><PERSON>", "中英混合": "Mélange de chinois et d'anglais", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "Audio de référence principal (Veuillez télécharger un audio de référence entre 3 et 10 secondes, dépasser cette limite entraînera une erreur !)", "主参考音频的文本": "Texte de l’audio de référence principal", "主参考音频的语种": "Langue de l’audio de référence principal", "也可批量输入音频文件, 二选一, 优先读文件夹": "Également possible d'entrer en lot des fichiers audio, au choix, privilégiez la lecture du dossier", "人声伴奏分离批量处理， 使用UVR5模型。": "Traitement par lot de séparation voix-accompagnement en utilisant le modèle UVR5.", "人声分离WebUI": "Interface Web de séparation des voix", "人声提取激进程度": "Degré d'extraction des voix", "以下文件或文件夹不存在": "<PERSON><PERSON><PERSON> ou Dossier de ce Type", "以下模型不存在:": "Aucun Modèle de ce Type:", "伴奏人声分离&去混响&去回声": "Séparation de la voix et de l'accompagnement, suppression de la réverbération et de l'écho", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "Vous n'avez pas téléchargé les paramètres du modèle d'upscaling, donc l'upscaling ne sera pas effectué. Veuillez suivre le tutoriel pour télécharger les fichiers nécessaires", "使用无参考文本模式时建议使用微调的GPT": "Il est recommandé d'utiliser un GPT finement ajusté lors de l'utilisation du mode sans texte de référence.", "保存频率save_every_epoch": "<PERSON><PERSON><PERSON> de sauvegarde (sauvegarder à chaque époque)", "保持随机": "Garder aléatoire", "关闭": "<PERSON><PERSON><PERSON> ", "凑50字一切": "Assembler 50 mots tout", "凑四句一切": "Composez quatre phrases pour tout remplir", "分桶处理模式已关闭": "Mode de traitement par regroupement désactivé", "分桶处理模式已开启": "Mode de traitement par regroupement activé", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "Le mode de retour segmenté ne prend pas en charge le regroupement des données, désactivation automatique", "分段返回模式已开启": "Mode de retour segmenté activé", "分段间隔(秒)": "Intervalle de segmentation (secondes)", "分段间隔过小，已自动设置为0.01": "L’intervalle de segmentation est trop court, réglé automatiquement à 0.01", "切分": "Segmentation", "切分后文本": "Texte après segmentation", "切分后的子音频的输出根目录": "Répertoire racine de sortie des sous-audios après découpage", "切分文本": "Segmenter le texte", "切割使用的进程数": "Nombre de processus utilisés pour le découpage", "刷新模型路径": "Actualiser le chemin du modèle", "前端处理后的文本(每句):": "Texte après traitement frontal (par phrase):", "前置数据集获取工具": "Outil de récupération des ensembles de données", "占用中": " <PERSON><PERSON><PERSON><PERSON>", "去混响/去延迟，附：": "Suppression de la réverbération / suppression du retard, ci-joint:", "参考音频在3~10秒范围外，请更换！": "Veuillez remplacer l'audio de référence si sa durée est en dehors de la plage de 3 à 10 secondes!", "参考音频的文本": "Texte de l'audio de référence", "参考音频的语种": "Langue de l'audio de référence", "句间停顿秒数": "Temps de pause entre les phrases (secondes)", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Optionnel : Téléchargez plusieurs fichiers audio de référence en les faisant glisser (recommandé d'être du même genre) et fusionnez leur tonalité. Si cette option est laissée vide, la tonalité sera contrôlée par l'unique fichier audio de référence à gauche. Si vous ajustez le modèle, il est recommandé que tous les fichiers audio de référence aient des tonalités dans l'ensemble d'entraînement d'ajustement ; le modèle pré-entrainé peut être ignoré.", "合成语音": "Synthèse vocale", "合成音频": "Synthétiser l'audio", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Exemple de format de chemin de dossier valide : E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (copiez-le depuis la barre d'adresse de l'explorateur de fichiers).", "后续将支持转音素、手工修改音素、语音合成分步执行。": "Le support pour la conversion phonémique, l’édition manuelle des phonèmes et la synthèse vocale par étapes sera ajouté ultérieurement.", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "Si vous ne comprenez pas bien l'audio de référence (vous ne savez pas quoi écrire), vous pouvez activer cette option. Une fois activée, le texte de référence sera ignoré.", "启用并行推理版本": "Activer la version d’inférence parallèle", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Veuillez indiquer le répertoire contenant les audio découpés ! Le chemin complet du fichier audio à lire = ce répertoire - nom du fichier correspondant à l'onde dans le fichier .list (pas le chemin complet). Si laissé vide, le chemin absolu dans le fichier .list sera utilisé.", "多语种混合": "Mélange multilingue", "多语种混合(粤语)": "Mélange Multilingue (Cantonais)", "失败": " Échec", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "Si vous n'acceptez pas ces conditions, vous ne pouvez ni utiliser ni citer aucun code ou fichier du package logiciel. Voir LICENSE à la racine.", "实际输入的参考文本:": "Texte de référence réellement saisi:", "实际输入的目标文本(切句后):": "Texte cible réellement saisi (après découpage):", "实际输入的目标文本(每句):": "Texte cible réellement saisi (par phrase):", "实际输入的目标文本:": "Texte cible réellement saisi:", "导出文件格式": "Format d'exportation du fichier", "已关闭": " <PERSON><PERSON><PERSON>", "已完成": " <PERSON><PERSON><PERSON><PERSON>", "已开启": " Activé", "并行合成中": "Synthèse parallèle en cours", "并行推理": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "并行推理模式已关闭": "Mode d’inférence parallèle désactivé", "并行推理模式已开启": "Mode d’inférence parallèle activé", "底模缺失，无法加载相应 LoRA 权重": "<PERSON><PERSON><PERSON><PERSON> de base manquant, impossible de charger les poids LoRA correspondants", "开启": "Activer ", "开启无参考文本模式。不填参考文本亦相当于开启。": "Activer le mode sans texte de référence. Laisser le texte de référence vide équivaut également à activer le mode.", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "Lorsque le mode d'inférence parallèle est activé, les modèles SoVITS V3/4 ne prennent pas en charge le traitement par lots, qui a donc été désactivé automatiquement.", "微调模型信息": "Informations sur le modèle affiné", "微调训练": "Entraînement de fine-tuning", "怎么切": "Comment d<PERSON>", "总训练轮数total_epoch": "Nombre total d'époques d'entraînement", "总训练轮数total_epoch，不建议太高": "Nombre total d'époques d'entraînement, pas recommandé d'être trop élevé", "指定输出主人声文件夹": "Spécifier le dossier de sortie pour la voix principale", "指定输出非主人声文件夹": "Spécifier le dossier de sortie pour la non-voix principale", "按中文句号。切": "Couper selon les points en chinois.", "按标点符号切": "Couper selon les signes de ponctuation", "按英文句号.切": "Découpez par des points en anglais", "推理": "Inférence", "推理设置": "Paramètres d’inférence", "提取文本Bert特征": "Extraire les caractéristiques du texte avec BERT", "数据分桶(并行推理时会降低一点计算量)": "Regroupement des données (Réduit le coût de calcul en inférence parallèle)", "数据类型精度": "précision du type de données", "文本分词与特征提取": "Segmentation et extraction de caractéristiques du texte", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "Outil de segmentation de texte. Un texte trop long peut donner un mauvais résultat, il est donc recommandé de le segmenter. La synthèse se fera selon les sauts de ligne puis sera assemblée.", "文本模块学习率权重": "Poids du taux d'apprentissage du module de texte", "施工中，请静候佳音": "En construction, veuillez attendre patiemment", "日文": "Japonais", "日英混合": "<PERSON><PERSON><PERSON><PERSON>", "是否仅保存最新的权重文件以节省硬盘空间": "Faut-il ne conserver que les derniers fichiers de poids pour économiser de l'espace disque ?", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Sauvegarder le petit modèle final dans le dossier weights à chaque point de sauvegarde", "是否开启DPO训练选项(实验性)": "Activer l'option d'entraînement DPO (expérimental) ?", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Ajuster la vitesse de parole et la tonalité du dernier résultat de synthèse pour prévenir l'aléatoire.", "显卡信息": "Informations sur la carte graphique", "未下载模型": "<PERSON>d<PERSON><PERSON> non téléchargé", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "Ce logiciel est open-source sous licence MIT. L'auteur n'exerce aucun contrôle sur le logiciel. L'utilisateur et toute personne diffusant les sorties audio générées sont entièrement responsables.", "标注文件路径 (含文件后缀 *.list)": "Chemin du fichier d'annotation (avec l'extension *.list)", "模型": "<PERSON><PERSON><PERSON><PERSON>", "模型分为三类：": "Les modèles sont classés en trois catégories:", "模型切换": "Changement de modèle", "模型加载中，请等待": "Le modèle est en cours de chargement, veuillez patienter...", "每张显卡的batch_size": "Taille de lot par carte graphique", "粤英混合": "Mélange Cantonais-Anglais", "粤语": "Cantonais", "终止合成": "Terminer la synthèse", "缺少Hubert数据集": "<PERSON><PERSON> <PERSON><PERSON>", "缺少语义数据集": "<PERSON><PERSON> <PERSON> Don<PERSON>", "缺少音素数据集": "<PERSON><PERSON> <PERSON> Données de Phonèmes Manquant", "缺少音频数据集": "Jeu de Données Audio Manquant", "英文": "<PERSON><PERSON><PERSON>", "训练模型的版本": "Version du modèle entraîné", "训练集格式化一键三连": "Formatage de l'ensemble d'entraînement en un clic", "训练集格式化工具": "Outil de formatage des ensembles d'entraînement", "语义Token提取": "Extraction de tokens sémantiques", "语速": "Débit de parole", "语速调整，高为更快": "Ajuster la vitesse de parole, plus élevée pour plus rapide", "语速调节不支持分桶处理，已自动关闭分桶处理": "Le réglage de la vitesse vocale ne prend pas en charge le regroupement des données, désactivation automatique", "语音切分": "Segmentation vocale", "语音切分工具": "Outil de segmentation vocale", "语音文本校对标注工具": "Outil d'annotation et de correction des transcriptions vocales", "语音自监督特征提取": "Extraction de caractéristiques auto-supervisée pour l'audio", "语音识别": "Reconnaissance vocale", "语音识别工具": "Outil de reconnaissance vocale", "语音降噪": "Réduction du bruit audio", "请上传3~10秒内参考音频，超过会报错！": "Veuillez télécharger une référence audio de 3 à 10 secondes ; les fichiers plus longs généreront une erreur!", "请上传参考音频": "Veuillez télécharger l'audio de référence", "请填入推理文本": "<PERSON><PERSON><PERSON>z remplir le texte cible", "请填入正确的List路径": "Veuillez Remplir le Chemin Correct de la Liste", "请填入正确的音频文件夹路径": "Veuillez Remplir le Chemin Correct du Dossier Audio", "请输入有效文本": "Veuillez entrer un texte valide", "路径不存在,使用默认配置": "Chemin introuvable, utilisation de la configuration par défaut", "路径不能为空": "Chemin Non Vide Attendu", "路径错误": "<PERSON><PERSON><PERSON>", "转换": "Conversion", "辅参考音频(可选多个，或不选)": "Audio de référence secondaire (Facultatif, plusieurs possibles ou aucun)", "输入待处理音频文件夹路径": "Entrez le chemin du dossier audio à traiter", "输入文件夹路径": "Chemin du dossier à entrer", "输入路径不存在": "Le chemin d'entrée n'existe pas", "输入路径存在但不可用": "Le chemin d'entrée existe mais est inutilisable", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Les fichiers et dossiers commençant par 23456 devraient être présents dans le répertoire logs/nom de l'expérience", "输出信息": "Sortie d'information", "输出文件夹路径": "Chemin du dossier de sortie", "输出的语音": "Audio de sortie", "运行中": " en cours d'exécution", "进度": "Progression", "进程已终止": " <PERSON>us terminé", "进程输出信息": " Sortie du processus", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "Sélectionnez les modèles entraînés stockés dans SoVITS_weights et GPT_weights. <PERSON><PERSON> <PERSON><PERSON><PERSON>, certains sont des modèles de base pour l'inférence Zero Shot TTS en 5 secondes sans entraînement.", "采样步数(仅对V3/4生效)": "Nombre d'étapes d'échantillonnage (uniquement effectif pour V3/4)", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "Nombre d’étapes d’échantillonnage : si le son est bruité, essayez d’augmenter, si c’est lent, essayez de ré<PERSON>ire", "重复惩罚": "Pénalité de répétition", "随机种子": "Graine aléatoire", "需先终止才能开启下一次任务": "Il faut d'abord arrêter le processus avant de lancer une nouvelle tâche", "需要合成的切分前文本": "Texte avant segmentation pour la synthèse", "需要合成的文本": "Texte à synthétiser", "需要合成的文本的语种": "Langue du texte à synthétiser", "需要合成的语种": "Langue de synthèse requise", "韩文": "<PERSON><PERSON>", "韩英混合": "<PERSON><PERSON><PERSON><PERSON>-Inglés", "音频加载失败": "Échec du Chargement de l'Audio", "音频文件不存在，跳过：": "Fichier audio introuvable, passage : ", "音频标注WebUI": "Interface Web d'annotation audio", "音频自动切分输入路径，可文件可文件夹": "Chemin d'entrée automatique de découpage audio, peut être un fichier ou un dossier", "音频超分中": "Super-résolution audio en cours", "音频超采样": "Suréchantillonnage audio", "音频超采样(仅对V3生效))": "Suréchantillonnage audio (uniquement effectif pour V3)", "预测语义Token": "Prédire le jeton sémantique", "预训练GPT模型路径": "Chemin du modèle GPT pré-entraîné", "预训练SSL模型路径": "Chemin du modèle SSL pré-entraîné", "预训练SoVITS-D模型路径": "Chemin du modèle SoVITS-D pré-entraîné", "预训练SoVITS-G模型路径": "Chemin du modèle SoVITS-G pré-entraîné", "预训练中文BERT模型路径": "Chemin du modèle BERT chinois pré-entraîné", "预训练模型路径": "Chemin du modèle pré-entraîné"}