{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb)：對於雙通道混響是最好的選擇，不能去除單通道混響；", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho：去除延遲效果。Aggressive 比 Normal 去除得更徹底，DeReverb 額外去除混響，可去除單聲道混響，但是對高頻重的板式混響去不乾淨。", "*实验/模型名": "*實驗/模型名", "*文本标注文件": "*文本標注文件", "*训练集音频文件目录": "*訓練集音頻文件目錄", "*请上传并填写参考信息": "*請上傳並填寫參考資訊", "*请填写需要合成的目标文本和语种模式": "請填寫需要合成的目標文本和語言模式", ".限制范围越小判别效果越好。": ".限制范围越小判别效果越好。", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1、DeEcho-DeReverb 模型的耗時是另外兩個 DeEcho 模型的接近兩倍；", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1、保留人聲：不帶和聲的音頻選這個，對主人聲保留比HP5更好。內置HP2和HP3兩個模型，HP3可能輕微漏伴奏但對主人聲保留比HP2稍微好一丁點；", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-變聲", "2、MDX-Net-Dereverb模型挺慢的；": "2、MDX-Net-Dereverb模型挺慢的；", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2、僅保留主人聲：帶和聲的音頻選這個，對主人聲可能有削弱。內置HP5一個模型；", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3、個人推薦的最乾淨的配置是先 MDX-Net 再 DeEcho-Aggressive。", "3、去混响、去延迟模型（by FoxJoy）：": "3、去混響、去延遲模型（by FoxJoy）：", "ASR 模型": "ASR 模型", "ASR 模型尺寸": "ASR 模型尺寸", "ASR 语言设置": "ASR 語言設置", "CPU训练,较慢": "CPU訓練，較慢", "GPT 训练: 模型权重文件在 GPT_weights/": "GPT 訓練: 模型權重文件在 GPT_weights/", "GPT模型列表": "GPT模型列表", "GPT训练": "GPT訓練", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "GPT 采样参数（无参考文本时不要太低。不懂就用默认）：", "GPU卡号,只能填1个整数": "GPU卡號,只能填1個整數", "GPU卡号以-分割，每个卡号一个进程": "GPU卡號以-分割，每個卡號一個進程", "LoRA秩": "LoRA階", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "SoVITS 訓練: 模型權重文件在 SoVITS_weights/", "SoVITS模型列表": "SoVITS模型列表", "SoVITS训练": "SoVITS訓練", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "送出文字：手動儲存目前頁面所有文字欄位內容至記憶體與檔案。若您在換頁前後或離開標註頁面前未按下此按鈕，當您返回時異動將會復原，辛苦的工作便會白費。", "TTS推理WebUI": "TTS推理WebUI", "UVR5人声伴奏分离&去混响去延迟工具": "UVR5人聲伴奏分離&去混響去延遲工具", "V3不支持无参考文本模式，请填写参考文本！": "V3 不支援無參考文字模式，請填寫參考文字！", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix:混多少比例歸一化後音頻進來", "batch_size": "批次大小", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size:怎麼算音量曲線，越小精度越大計算量越高（不是精度越大效果越好）", "max:归一化后最大值多少": "max:歸一化後最大值多少", "max_sil_kept:切完后静音最多留多长": "max_sil_kept:切完後靜音最多留多長", "min_interval:最短切割间隔": "min_interval:最短切割間隔", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length:每段最小多長，如果第一段太短一直和後面段連起來直到超過這個值", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "threshold:音量小於這個值視作靜音的備選切割點", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "v3暫不支援該模式，使用了會報錯。", "v3输出如果觉得闷可以试试开超分": "v3輸出如果覺得悶可以試試開超分", "不切": "不切", "不训练直接推v2ProPlus底模！": "不訓練直接使用v2ProPlus底模！", "不训练直接推v2Pro底模！": "不訓練直接使用v2Pro底模！", "不训练直接推v2底模！": "不訓練直接使用v2底模！", "不训练直接推v3底模！": "不訓練直接使用v3底模！", "中文": "中文", "中英混合": "中英混合", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "主參考音檔（請上傳3~10秒內參考音檔，超過會報錯！）", "主参考音频的文本": "主參考音檔的文字", "主参考音频的语种": "主參考音檔的語種", "也可批量输入音频文件, 二选一, 优先读文件夹": "也可批量输入音频文件, 二选一, 优先读文件夹", "人声伴奏分离批量处理， 使用UVR5模型。": "人聲伴奏分離批量處理， 使用UVR5模型。", "人声分离WebUI": "人聲分離WebUI", "人声提取激进程度": "人聲提取激進程度", "以下文件或文件夹不存在": "沒有此文件或文件夾", "以下模型不存在:": "#以下模型不存在", "伴奏人声分离&去混响&去回声": "伴奏人聲分離&去混響&去回聲", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "你沒有下載超分模型的參數，因此不進行超分。如想超分請先參照教程把文件下載好", "使用无参考文本模式时建议使用微调的GPT": "使用無參考文本模式時建議使用微調的GPT", "保存频率save_every_epoch": "保存頻率save_every_epoch", "保持随机": "保持隨機", "关闭": "關閉", "凑50字一切": "湊50字一切", "凑四句一切": "湊四句一切", "分桶处理模式已关闭": "分桶處理模式已關閉", "分桶处理模式已开启": "分桶處理模式已開啟", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "分段返回模式不支援分桶處理，已自動關閉分桶處理", "分段返回模式已开启": "分段返回模式已開啟", "分段间隔(秒)": "分段間隔（秒）", "分段间隔过小，已自动设置为0.01": "分段間隔過小，已自動設定為0.01", "切分": "切分", "切分后文本": "切分後文字", "切分后的子音频的输出根目录": "切分後的子音頻的輸出根目錄", "切分文本": "切分文字", "切割使用的进程数": "切割使用的進程數", "刷新模型路径": "刷新模型路徑", "前端处理后的文本(每句):": "前端處理後的文本(每句):", "前置数据集获取工具": "前置數據集獲取工具", "占用中": "佔用中", "去混响/去延迟，附：": "去混響/去延遲，附：", "参考音频在3~10秒范围外，请更换！": "參考音頻在3~10秒範圍外，請更換！", "参考音频的文本": "參考音頻的文本", "参考音频的语种": "參考音頻的語種", "句间停顿秒数": "句間停頓秒數", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "可選項：通過拖曳多個文件上傳多個參考音頻（建議同性），平均融合他們的音色。如不填寫此項，音色由左側單個參考音頻控制。如是微調模型，建議參考音頻全部在微調訓練集音色內，底模不用管。", "合成语音": "合成語音", "合成音频": "合成音訊", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "合格的資料夾路徑格式舉例： E:\\codes\\py39\\vits_vc_gpu\\白鷺霜華測試範例(去文件管理器地址欄拷就行了)。", "后续将支持转音素、手工修改音素、语音合成分步执行。": "後續將支援轉音素、手工修改音素、語音合成分步執行。", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "聽不清參考音頻說的啥(不曉得寫啥)可以開，開啟後無視填寫的參考文本。", "启用并行推理版本": "啟用並行推理版本", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "填切割後音頻所在目錄！讀取的音頻檔案完整路徑=該目錄-拼接-list檔案裡波形對應的檔案名（不是全路徑）。如果留空則使用.list檔案裡的絕對全路徑。", "多语种混合": "多語種混合", "多语种混合(粤语)": "多語種混合 (粵語)", "失败": "失敗", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "如不認可該條款，則不能使用或引用軟體包內任何代碼和文件。詳見根目錄LICENSE", "实际输入的参考文本:": "實際輸入的參考文本:", "实际输入的目标文本(切句后):": "實際輸入的目標文本(切句後):", "实际输入的目标文本(每句):": "實際輸入的目標文本(每句):", "实际输入的目标文本:": "實際輸入的目標文本:", "导出文件格式": "導出檔格式", "已关闭": "已關閉", "已完成": "已完成", "已开启": "已開啟", "并行合成中": "平行合成中", "并行推理": "並行推理", "并行推理模式已关闭": "並行推理模式已關閉", "并行推理模式已开启": "並行推理模式已開啟", "底模缺失，无法加载相应 LoRA 权重": "底模缺失，無法載入相應 LoRA 權重", "开启": "開啟", "开启无参考文本模式。不填参考文本亦相当于开启。": "開啟無參考文本模式。不填參考文本亦相當於開啟。", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "啟用平行推論模式時，SoVITS V3/4 模型不支援分倉處理，已自動關閉該功能。", "微调模型信息": "微調模型資訊", "微调训练": "微調訓練", "怎么切": "怎麼切", "总训练轮数total_epoch": "總訓練輪數total_epoch", "总训练轮数total_epoch，不建议太高": "總訓練輪數total_epoch，不建議太高", "指定输出主人声文件夹": "指定输出主人声文件夹", "指定输出非主人声文件夹": "指定输出非主人声文件夹", "按中文句号。切": "按中文句號。切", "按标点符号切": "按標點符號切", "按英文句号.切": "按英文句號.切", "推理": "推理", "推理设置": "推理設定", "提取文本Bert特征": "提取文字BERT特徵", "数据分桶(并行推理时会降低一点计算量)": "資料分桶（並行推理時會降低一點計算量）", "数据类型精度": "數據類型精度", "文本分词与特征提取": "文本分詞與特徵提取", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "文字切分工具。太長的文字合成出來效果不一定好，所以太長建議先切。合成會根據文字的換行分開合成再拼起來。", "文本模块学习率权重": "文本模塊學習率權重", "施工中，请静候佳音": "施工中，請靜候佳音", "日文": "日文", "日英混合": "日英混合", "是否仅保存最新的权重文件以节省硬盘空间": "是否僅保存最新的權重文件以節省硬盤空間", "是否在每次保存时间点将最终小模型保存至weights文件夹": "是否在每次保存時間點將最終小模型保存至weights文件夾", "是否开启DPO训练选项(实验性)": "是否開啟DPO訓練選項(實驗性)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "是否直接對上次合成結果調整語速和音色，以防止隨機性。", "显卡信息": "顯卡資訊", "未下载模型": "未下載模型", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "本軟體以MIT協議開源，作者不對軟體具備任何控制力，使用軟體者、傳播軟體導出的聲音者自負全責。", "标注文件路径 (含文件后缀 *.list)": "標註文件路徑 (含文件後綴 *.list)", "模型": "模型", "模型分为三类：": "模型分為三類：", "模型切换": "模型切換", "模型加载中，请等待": "模型載入中，請稍候", "每张显卡的batch_size": "每張顯卡的batch_size", "粤英混合": "粵英混合", "粤语": "粵語", "终止合成": "終止合成", "缺少Hubert数据集": "缺少Hubert數據集", "缺少语义数据集": "缺少語義數據集", "缺少音素数据集": "缺少音素數據集", "缺少音频数据集": "缺少音頻數據集", "英文": "英文", "训练模型的版本": "訓練模型的版本", "训练集格式化一键三连": "訓練集格式化一鍵三連", "训练集格式化工具": "訓練集格式化工具", "语义Token提取": "語義Token提取", "语速": "語速", "语速调整，高为更快": "調整語速，高為更快", "语速调节不支持分桶处理，已自动关闭分桶处理": "語速調節不支援分桶處理，已自動關閉分桶處理", "语音切分": "語音切分", "语音切分工具": "語音切分工具", "语音文本校对标注工具": "語音文本校對標註工具", "语音自监督特征提取": "語音自監督特徵提取", "语音识别": "語音識別", "语音识别工具": "語音識別工具", "语音降噪": "語音降噪", "请上传3~10秒内参考音频，超过会报错！": "請上傳3~10秒內參考音頻，超過會報錯！", "请上传参考音频": "請上傳參考音頻", "请填入推理文本": "請填入推理文本", "请填入正确的List路径": "請填寫正確的列表路徑", "请填入正确的音频文件夹路径": "請填寫正確的音頻文件夾路徑", "请输入有效文本": "請輸入有效文本", "路径不存在,使用默认配置": "路徑不存在，使用預設配置", "路径不能为空": "路徑不應該為空", "路径错误": "路徑錯誤", "转换": "轉換", "辅参考音频(可选多个，或不选)": "輔參考音檔（可選多個，或不選）", "输入待处理音频文件夹路径": "輸入待處理音頻資料夾路徑", "输入文件夹路径": "輸入文件夾路徑", "输入路径不存在": "輸入路徑不存在", "输入路径存在但不可用": "輸入路徑存在但不可用", "输出logs/实验名目录下应有23456开头的文件和文件夹": "輸出logs/實驗名目錄下應有23456開頭的文件和文件夾", "输出信息": "輸出訊息", "输出文件夹路径": "輸出文件夾路徑", "输出的语音": "輸出的語音", "运行中": "運行中", "进度": "進度", "进程已终止": "進程已終止", "进程输出信息": "進程輸出資訊", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "選擇訓練完成存放在SoVITS_weights和GPT_weights下的模型。預設的幾個是底模，體驗5秒Zero Shot TTS不訓練推理使用。", "采样步数(仅对V3/4生效)": "取樣步數（僅適用於 V3/4）", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "採樣步數，如果覺得電，提高試試，如果覺得慢，降低試試", "重复惩罚": "重複懲罰", "随机种子": "隨機種子", "需先终止才能开启下一次任务": "需先終止才能開啟下一次任務", "需要合成的切分前文本": "需要合成的切分前文字", "需要合成的文本": "需要合成的文本", "需要合成的文本的语种": "需要合成的文字語種", "需要合成的语种": "需要合成的語種", "韩文": "韓文", "韩英混合": "韓英混合", "音频加载失败": "無法加載音頻", "音频文件不存在，跳过：": "音訊檔案不存在，跳過：", "音频标注WebUI": "音訊標註 WebUI", "音频自动切分输入路径，可文件可文件夹": "音訊自動切割輸入路徑，可為檔案或資料夾", "音频超分中": "音訊超高解析度處理中", "音频超采样": "音訊超取樣", "音频超采样(仅对V3生效))": "音訊超取樣（僅適用於 V3）", "预测语义Token": "預測語意 Token", "预训练GPT模型路径": "預訓練 GPT 模型路徑", "预训练SSL模型路径": "預訓練 SSL 模型路徑", "预训练SoVITS-D模型路径": "預訓練 SoVITS-D 模型路徑", "预训练SoVITS-G模型路径": "預訓練 SoVITS-G 模型路徑", "预训练中文BERT模型路径": "預訓練中文BERT模型路徑", "预训练模型路径": "預訓練模型路徑"}