{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net (onnx_dereverb): 듀얼 채널 리버브에는 가장 적합하지만, 싱글 채널 리버브는 제거할 수 없습니다", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:지연 효과를 제거합니다. Aggressive는 Normal보다 더 철저하게 제거하며, DeReverb는 추가로 리버브를 제거하여 단일 채널 리버브를 제거할 수 있지만 고주파 리버브는 완전히 제거하지 못합니다.", "*实验/模型名": "*실험/모델 이름", "*文本标注文件": "*텍스트 주석 파일", "*训练集音频文件目录": "*훈련 세트 오디오 파일 디렉터리", "*请上传并填写参考信息": "*참고 정보를 업로드하고 입력하십시오", "*请填写需要合成的目标文本和语种模式": "*합성할 목표 텍스트와 언어 모드를 입력하세요", ".限制范围越小判别效果越好。": "다언어 지원을 줄이는 것이 더 좋습니다", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. <PERSON><PERSON><PERSON><PERSON>DeReverb 모델의 처리 시간은 다른 두 DeEcho 모델의 거의 두 배입니다;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. 사람 목소리를 유지: 화음이 없는 오디오를 선택하면 HP5보다 사람 목소리를 더 잘 유지할 수 있습니다. 내장된 HP2와 HP3 모델이 있으며, HP3는 화음을 약간 놓칠 수 있지만 HP2보다 사람 목소리를 조금 더 잘 유지합니다;", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-음성 변환", "2、MDX-Net-Dereverb模型挺慢的；": "2. MDX-Net-Dereverb 모델은 꽤 느립니다;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. 주 목소리만 유지: 화음이 있는 오디오에 이 모델을 선택하면 주 목소리가 약해질 수 있습니다. 내장된 HP5 모델이 있습니다;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. 개인적으로 가장 깨끗한 설정은 먼저 MDX-Net을 사용하고 그 다음에 DeEcho-Aggressive를 사용하는 것입니다;", "3、去混响、去延迟模型（by FoxJoy）：": "3. 잔향 제거 및 지연 제거 모델 (by <PERSON><PERSON><PERSON>):", "ASR 模型": "ASR 모델", "ASR 模型尺寸": "ASR 모델 크기", "ASR 语言设置": "ASR 언어 설정", "CPU训练,较慢": "CPU 학습, 속도 느림", "GPT 训练: 模型权重文件在 GPT_weights/": "GPT 훈련: 모델 가중치 파일은 GPT_weights/에 있습니다", "GPT模型列表": "GPT 모델 목록", "GPT训练": "GPT훈련", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "GPT 샘플링 매개변수 (참조 텍스트가 없을 때 너무 낮게 설정하지 마십시오. 확실하지 않으면 기본값을 사용하십시오):", "GPU卡号,只能填1个整数": "GPU 카드 번호, 1개의 정수만 입력 가능", "GPU卡号以-分割，每个卡号一个进程": "GPU 카드 번호는 -로 구분되며 각 카드 번호에 하나의 프로세스가 있어야 함", "LoRA秩": "LoRA 랭크", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "SoVITS 훈련: 모델 가중치 파일은 SoVITS_weights/에 있습니다", "SoVITS模型列表": "SoVITS 모델 목록", "SoVITS训练": "SoVITS훈련", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "현재 페이지의 모든 텍스트 상자 내용을 수동으로 메모리와 파일에 저장합니다. (페이지 전환 전후 또는 주석 작업을 종료하기 전에 이 버튼을 누르지 않으면, 다시 돌아왔을 때 변경 사항이 롤백되어 작업이 무효가 됩니다.)", "TTS推理WebUI": "TTS 추론 WebUI", "UVR5人声伴奏分离&去混响去延迟工具": "UVR5 보컬 및 반주 분리 & 리버브 제거 및 딜레이 제거 도구", "V3不支持无参考文本模式，请填写参考文本！": "V3는 참조 텍스트 없이 작동할 수 없습니다. 반드시 참조 텍스트를 입력해주세요!", "alpha_mix:混多少比例归一化后音频进来": "알파 믹스: 정규화된 오디오가 들어오는 비율", "batch_size": "배치 크기", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop 크기: 볼륨 곡선을 계산하는 방법. 작을수록 정확도가 높아지지만 계산량이 높아집니다 (정확도가 높다고 효과가 좋아지지 않음)", "max:归一化后最大值多少": "최대 값 (정규화 후)", "max_sil_kept:切完后静音最多留多长": "최대 유지되는 정적 길이 (분리 후)", "min_interval:最短切割间隔": "최소 분리 간격", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length:각 부분의 최소 길이, 첫 번째 부분이 너무 짧으면 다음 부분과 계속 연결하여 이 값을 초과할 때까지", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "임계 값: 이 값보다 작은 볼륨은 대체 분리 지점으로 간주됩니다.", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "v3에서는 이 모드를 지원하지 않으며, 사용 시 오류가 발생합니다.", "v3输出如果觉得闷可以试试开超分": "v3 출력이 답답하게 들리면 슈퍼 레졸루션을 켜보세요", "不切": "자르지 않음", "不训练直接推v2ProPlus底模！": "학습 없이 바로 v2ProPlus 베이스 모델 사용!", "不训练直接推v2Pro底模！": "학습 없이 바로 v2Pro 베이스 모델 사용!", "不训练直接推v2底模！": "학습 없이 바로 v2 베이스 모델 사용!", "不训练直接推v3底模！": "학습 없이 바로 v3 베이스 모델 사용!", "中文": "중국어", "中英混合": "중영 혼합", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "주 참조 오디오 (3~10초 이내의 참조 오디오를 업로드하세요. 초과 시 오류 발생!)", "主参考音频的文本": "주 참조 오디오의 텍스트", "主参考音频的语种": "주 참조 오디오의 언어", "也可批量输入音频文件, 二选一, 优先读文件夹": "오디오 파일을 일괄로 입력할 수도 있습니다. 둘 중 하나를 선택하고 폴더를 읽기를 우선합니다.", "人声伴奏分离批量处理， 使用UVR5模型。": "보컬과 반주 분리 배치 처리, UVR5 모델 사용.", "人声分离WebUI": "보컬 분리 WebUI", "人声提取激进程度": "보컬 추출의 공격성", "以下文件或文件夹不存在": "해당 파일이나 폴더가 없습니다", "以下模型不存在:": "해당 모델이 존재하지 않습니다:", "伴奏人声分离&去混响&去回声": "반주 및 보컬 분리 & 리버브 제거 & 에코 제거", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "슈퍼 레졸루션 모델의 파라미터를 다운로드하지 않았으므로 슈퍼 레졸루션을 수행하지 않습니다. 사용하려면 먼저 튜토리얼을 참고하여 파일을 다운로드하세요", "使用无参考文本模式时建议使用微调的GPT": "참고 텍스트 없이 사용할 경우 미세 조정된 GPT 사용을 권장합니다", "保存频率save_every_epoch": "저장 빈도 (각 라운드마다)", "保持随机": "랜덤 유지", "关闭": "닫기", "凑50字一切": "50자를 채우십시오", "凑四句一切": "네 문장의 세트를 완성하세요.", "分桶处理模式已关闭": "버킷 처리 모드 비활성화됨", "分桶处理模式已开启": "버킷 처리 모드 활성화됨", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "분할 반환 모드는 버킷 처리를 지원하지 않아 버킷 처리 자동 비활성화됨", "分段返回模式已开启": "분할 반환 모드 활성화됨", "分段间隔(秒)": "분할 간격(초)", "分段间隔过小，已自动设置为0.01": "분할 간격이 너무 짧아 자동으로 0.01로 설정됨", "切分": "분할", "切分后文本": "분할 후 추론 텍스트", "切分后的子音频的输出根目录": "분리된 하위 오디오의 출력 기본 디렉터리", "切分文本": "텍스트 분할", "切割使用的进程数": "사용되는 프로세스 수로 자르기", "刷新模型路径": "모델 경로 새로 고침", "前端处理后的文本(每句):": "프론트엔드 처리 후 텍스트(문장별):", "前置数据集获取工具": "전처리 데이터셋 획득 도구", "占用中": "사용 중", "去混响/去延迟，附：": "리버브 제거/지연 제거, 부록:", "参考音频在3~10秒范围外，请更换！": "참고 오디오가 3~10초 범위를 벗어났습니다. 다른 것으로 바꾸십시오!", "参考音频的文本": "참고 오디오의 텍스트", "参考音频的语种": "참고 오디오의 언어", "句间停顿秒数": "문장 간 정지 시간 (초)", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "선택 사항: 여러 참조 오디오 파일을 드래그하여 업로드하고 (동일한 성별을 권장), 그들의 톤을 평균화합니다. 이 옵션을 비워두면 톤은 왼쪽의 단일 참조 오디오로 제어됩니다. 모델을 미세 조정하는 경우 모든 참조 오디오 파일이 미세 조정 훈련 세트 내의 톤을 가지고 있는 것이 좋으며, 사전 훈련된 모델은 무시할 수 있습니다.", "合成语音": "합성 음성", "合成音频": "오디오 생성", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "적절한 폴더 경로 형식 예: E:\\codes\\py39\\vits_vc_gpu\\백로서리 테스트 샘플 (파일 관리자 주소 표시줄에서 복사하면 됩니다).", "后续将支持转音素、手工修改音素、语音合成分步执行。": "향후 음소 변환, 수동 음소 편집, 단계별 음성 합성 지원이 추가될 예정입니다.", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "참고 오디오의 내용을 정확히 알아들을 수 없을 경우 이 옵션을 활성화하세요. 활성화하면 입력한 참고 텍스트를 무시합니다.", "启用并行推理版本": "병렬 추론 버전 활성화", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "분리된 오디오가 위치한 디렉터리를 입력하세요! 읽어들인 오디오 파일의 전체 경로 = 이 디렉터리 - list 파일에서 파형에 해당하는 파일명(전체 경로가 아님). 비워 두면 .list 파일의 절대 전체 경로를 사용합니다.", "多语种混合": "다국어 혼합", "多语种混合(粤语)": "다국어 혼합(粤語)", "失败": "실패", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "이 조항에 동의하지 않는다면, 소프트웨어 패키지 내의 코드 및 파일을 사용할 수 없습니다. 자세한 내용은 루트 디렉토리 LICENSE를 참조하세요.", "实际输入的参考文本:": "실제 입력된 참고 텍스트:", "实际输入的目标文本(切句后):": "실제 입력된 목표 텍스트(문장 분리 후):", "实际输入的目标文本(每句):": "실제 입력된 목표 텍스트(문장별):", "实际输入的目标文本:": "실제 입력된 목표 텍스트:", "导出文件格式": "내보내기 파일 형식", "已关闭": "닫힘", "已完成": "완료됨", "已开启": "켜짐", "并行合成中": "병렬 오디오 생성 중", "并行推理": "병렬 추론", "并行推理模式已关闭": "병렬 추론 모드 비활성화됨", "并行推理模式已开启": "병렬 추론 모드 활성화됨", "底模缺失，无法加载相应 LoRA 权重": "기본 모델이 없어서 해당 LoRA 가중치를 로드할 수 없습니다", "开启": "켜기", "开启无参考文本模式。不填参考文本亦相当于开启。": "참고 텍스트 없이 모드를 활성화합니다. 참고 텍스트를 입력하지 않으면 자동으로 활성화됩니다.", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "병렬 추론 모드가 활성화된 경우, SoVITS V3/4 모델은 버킷 처리를 지원하지 않으며, 자동으로 비활성화됩니다.", "微调模型信息": "미세 조정(Fine-tuning) 모델 정보", "微调训练": "미세 조정 훈련", "怎么切": "자르기 옵션", "总训练轮数total_epoch": "총 훈련 라운드 수 (total_epoch)", "总训练轮数total_epoch，不建议太高": "총 훈련 라운드 수 (total_epoch), 너무 높지 않게 권장됨", "指定输出主人声文件夹": "지정된 주인 목소리 출력 폴더", "指定输出非主人声文件夹": "지정된 비주인 목소리 출력 폴더", "按中文句号。切": "중국어 문장으로 분리하십시오.", "按标点符号切": "구두점을 기준으로 자르기", "按英文句号.切": "영어 문장으로 분리하기", "推理": "추론", "推理设置": "추론 설정", "提取文本Bert特征": "텍스트 BERT 특징 추출", "数据分桶(并行推理时会降低一点计算量)": "데이터 버킷팅 (병렬 추론 시 계산량 감소)", "数据类型精度": "데이터 유형 정밀도", "文本分词与特征提取": "텍스트 토큰화 및 특성 추출", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "텍스트 분할 도구. 너무 긴 텍스트는 합성 결과가 좋지 않을 수 있으므로 분할을 권장합니다. 합성은 텍스트의 줄바꿈을 기준으로 분할된 후 결합됩니다.", "文本模块学习率权重": "텍스트 모듈 학습률 가중치", "施工中，请静候佳音": "공사 중입니다. 기다려주십시오.", "日文": "일본어", "日英混合": "일본어와 영어 혼합", "是否仅保存最新的权重文件以节省硬盘空间": "디스크 공간을 절약하기 위해 최신 가중치 파일만 저장할지 여부", "是否在每次保存时间点将最终小模型保存至weights文件夹": "각 저장 시간에 최종 작은 모델을 weights 폴더에 저장할지 여부", "是否开启DPO训练选项(实验性)": "DPO 훈련 옵션 활성화 여부 (실험적 기능)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "랜덤성을 방지하기 위해 마지막 합성 결과의 말하기 속도와 톤을 조정합니다.", "显卡信息": "그래픽 카드 정보", "未下载模型": "모델이 다운로드되지 않음", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "본 소프트웨어는 MIT 라이선스로 오픈소스이며, 개발자는 소프트웨어에 대한 어떠한 통제력도 가지지 않습니다. 사용자는 소프트웨어를 이용하거나 이를 통해 생성된 음성을 배포할 경우 모든 책임을 집니다.", "标注文件路径 (含文件后缀 *.list)": "주석 파일 경로 (*.list 확장자 포함)", "模型": "모델", "模型分为三类：": "모델은 3가지로 나뉩니다：", "模型切换": "모델 전환", "模型加载中，请等待": "모델을 불러오는 중입니다. 잠시 기다려주세요...", "每张显卡的batch_size": "각 그래픽 카드의 배치 크기", "粤英混合": "粤영 혼합", "粤语": "粤語", "终止合成": "합성 종료", "缺少Hubert数据集": "<PERSON> 데이터셋이 없습니다", "缺少语义数据集": "의미론 데이터셋이 없습니다", "缺少音素数据集": "음소 데이터셋이 없습니다", "缺少音频数据集": "오디오 데이터셋이 없습니다", "英文": "영어", "训练模型的版本": "학습된 모델 버전", "训练集格式化一键三连": "훈련 데이터셋 포맷팅 원클릭 실행", "训练集格式化工具": "훈련 데이터셋 포맷팅 도구", "语义Token提取": "의미론적 토큰 추출", "语速": "언어 속도", "语速调整，高为更快": "언어 속도 조정, 높을수록 빠름", "语速调节不支持分桶处理，已自动关闭分桶处理": "음속 조절은 버킷 처리를 지원하지 않아 버킷 처리 자동 비활성화됨", "语音切分": "음성 분할", "语音切分工具": "음성 분할 도구", "语音文本校对标注工具": "음성 텍스트 교정 및 주석 도구", "语音自监督特征提取": "음성 자율 학습 특성 추출", "语音识别": "음성 인식", "语音识别工具": "음성 인식 도구", "语音降噪": "음성 잡음 제거", "请上传3~10秒内参考音频，超过会报错！": "3~10초 이내의 참고 오디오를 업로드하십시오. 초과하면 오류가 발생합니다!", "请上传参考音频": "참고 오디오를 업로드하세요", "请填入推理文本": "목표 텍스트를 입력하세요", "请填入正确的List路径": "올바른 리스트 경로를 입력하세요", "请填入正确的音频文件夹路径": "올바른 오디오 폴더 경로를 입력하세요", "请输入有效文本": "유효한 텍스트를 입력하세요", "路径不存在,使用默认配置": "경로가 존재하지 않음, 기본 설정 사용", "路径不能为空": "경로가 비어 있을 수 없습니다", "路径错误": "경로 오류", "转换": "변환", "辅参考音频(可选多个，或不选)": "보조 참조 오디오 (여러 개 선택 가능 또는 선택 안 함)", "输入待处理音频文件夹路径": "처리 대기 중인 오디오 폴더 경로 입력", "输入文件夹路径": "폴더 경로 입력", "输入路径不存在": "입력 경로가 존재하지 않음", "输入路径存在但不可用": "입력 경로가 존재하지만 사용할 수 없음", "输出logs/实验名目录下应有23456开头的文件和文件夹": "logs/실험 이름 디렉터리에는 23456으로 시작하는 파일과 폴더가 있어야 함", "输出信息": "출력 정보", "输出文件夹路径": "출력 폴더 경로", "输出的语音": "출력 음성", "运行中": "실행 중", "进度": "진행 상태", "进程已终止": "프로세스 종료됨", "进程输出信息": "프로세스 출력 정보", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "SoVITS_weights와 GPT_weights에 저장된 학습 완료 모델을 선택하세요. 기본 제공되는 모델은 베이스 모델로, 5초 Zero Shot TTS 추론을 학습 없이 체험할 수 있습니다.", "采样步数(仅对V3/4生效)": "샘플링 단계 수 (V3/V4에만 적용됨)", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "샘플링 스텝: 노이즈가 느껴지면 증가, 느리다면 감소 시도", "重复惩罚": "반복 패널티", "随机种子": "랜덤 시드", "需先终止才能开启下一次任务": "다음 작업을 시작하려면 먼저 종료해야 합니다", "需要合成的切分前文本": "분할 전 추론 텍스트", "需要合成的文本": "합성해야 할 텍스트", "需要合成的文本的语种": "추론 텍스트의 언어", "需要合成的语种": "합성해야 할 언어", "韩文": "한국어", "韩英混合": "한영 혼합", "音频加载失败": "오디오 로드 실패", "音频文件不存在，跳过：": "오디오 파일이 존재하지 않음, 건너뜀: ", "音频标注WebUI": "오디오 주석 WebUI", "音频自动切分输入路径，可文件可文件夹": "오디오 자동 분리 입력 경로, 파일 또는 폴더 가능", "音频超分中": "오디오 슈퍼 레졸루션 처리 중", "音频超采样": "오디오 업샘플링", "音频超采样(仅对V3生效))": "오디오 업샘플링 (V3에만 적용됨)", "预测语义Token": "의미 기반 토큰 예측", "预训练GPT模型路径": "사전 학습된 GPT 모델 경로", "预训练SSL模型路径": "사전 학습된 SSL 모델 경로", "预训练SoVITS-D模型路径": "사전 학습된 SoVITS-D 모델 경로", "预训练SoVITS-G模型路径": "사전 학습된 SoVITS-G 모델 경로", "预训练中文BERT模型路径": "사전 학습된 중국어 BERT 모델 경로", "预训练模型路径": "사전 학습 모델 경로"}