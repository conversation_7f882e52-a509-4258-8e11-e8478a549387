{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:去除延迟效果。Aggressive 比 Normal 去除得更彻底，DeReverb 额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。", "*实验/模型名": "*实验/模型名", "*文本标注文件": "*文本标注文件", "*训练集音频文件目录": "*训练集音频文件目录", "*请上传并填写参考信息": "*请上传并填写参考信息", "*请填写需要合成的目标文本和语种模式": "*请填写需要合成的目标文本和语种模式", ".限制范围越小判别效果越好。": ".限制范围越小判别效果越好。", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1、De<PERSON><PERSON>-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-变声", "2、MDX-Net-Dereverb模型挺慢的；": "2、MDX-Net-Dereverb模型挺慢的；", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。", "3、去混响、去延迟模型（by FoxJoy）：": "3、去混响、去延迟模型（by FoxJoy）：", "ASR 模型": "ASR 模型", "ASR 模型尺寸": "ASR 模型尺寸", "ASR 语言设置": "ASR 语言设置", "CPU训练,较慢": "CPU训练,较慢", "GPT 训练: 模型权重文件在 GPT_weights/": "GPT 训练: 模型权重文件在 GPT_weights/", "GPT模型列表": "GPT模型列表", "GPT训练": "GPT训练", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "GPT采样参数(无参考文本时不要太低。不懂就用默认)：", "GPU卡号,只能填1个整数": "GPU卡号,只能填1个整数", "GPU卡号以-分割，每个卡号一个进程": "GPU卡号以-分割，每个卡号一个进程", "LoRA秩": "LoRA秩", "SoVITS 训练: 模型权重文件在 SoVITS_weights/": "SoVITS 训练: 模型权重文件在 SoVITS_weights/", "SoVITS模型列表": "SoVITS模型列表", "SoVITS训练": "SoVITS训练", "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)": "Submit Text: 将当前页所有文本框内容手工保存到内存和文件(翻页前后或者退出标注页面前如果没点这个按钮，你再翻回来就回滚了，白忙活。)", "TTS推理WebUI": "TTS推理WebUI", "UVR5人声伴奏分离&去混响去延迟工具": "UVR5人声伴奏分离&去混响去延迟工具", "V3不支持无参考文本模式，请填写参考文本！": "V3不支持无参考文本模式，请填写参考文本！", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix:混多少比例归一化后音频进来", "batch_size": "batch_size", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）", "max:归一化后最大值多少": "max:归一化后最大值多少", "max_sil_kept:切完后静音最多留多长": "max_sil_kept:切完后静音最多留多长", "min_interval:最短切割间隔": "min_interval:最短切割间隔", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "threshold:音量小于这个值视作静音的备选切割点", "top_k": "top_k", "top_p": "top_p", "v3暂不支持该模式，使用了会报错。": "v3暂不支持该模式，使用了会报错。", "v3输出如果觉得闷可以试试开超分": "v3输出如果觉得闷可以试试开超分", "不切": "不切", "不训练直接推v2ProPlus底模！": "不训练直接推v2ProPlus底模！", "不训练直接推v2Pro底模！": "不训练直接推v2Pro底模！", "不训练直接推v2底模！": "不训练直接推v2底模！", "不训练直接推v3底模！": "不训练直接推v3底模！", "中文": "中文", "中英混合": "中英混合", "主参考音频(请上传3~10秒内参考音频，超过会报错！)": "主参考音频(请上传3~10秒内参考音频，超过会报错！)", "主参考音频的文本": "主参考音频的文本", "主参考音频的语种": "主参考音频的语种", "也可批量输入音频文件, 二选一, 优先读文件夹": "也可批量输入音频文件, 二选一, 优先读文件夹", "人声伴奏分离批量处理， 使用UVR5模型。": "人声伴奏分离批量处理， 使用UVR5模型。", "人声分离WebUI": "人声分离WebUI", "人声提取激进程度": "人声提取激进程度", "以下文件或文件夹不存在": "以下文件或文件夹不存在", "以下模型不存在:": "以下模型不存在:", "伴奏人声分离&去混响&去回声": "伴奏人声分离&去混响&去回声", "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好": "你没有下载超分模型的参数，因此不进行超分。如想超分请先参照教程把文件下载好", "使用无参考文本模式时建议使用微调的GPT": "使用无参考文本模式时建议使用微调的GPT", "保存频率save_every_epoch": "保存频率save_every_epoch", "保持随机": "保持随机", "关闭": "关闭", "凑50字一切": "凑50字一切", "凑四句一切": "凑四句一切", "分桶处理模式已关闭": "分桶处理模式已关闭", "分桶处理模式已开启": "分桶处理模式已开启", "分段返回模式不支持分桶处理，已自动关闭分桶处理": "分段返回模式不支持分桶处理，已自动关闭分桶处理", "分段返回模式已开启": "分段返回模式已开启", "分段间隔(秒)": "分段间隔(秒)", "分段间隔过小，已自动设置为0.01": "分段间隔过小，已自动设置为0.01", "切分": "切分", "切分后文本": "切分后文本", "切分后的子音频的输出根目录": "切分后的子音频的输出根目录", "切分文本": "切分文本", "切割使用的进程数": "切割使用的进程数", "刷新模型路径": "刷新模型路径", "前端处理后的文本(每句):": "前端处理后的文本(每句):", "前置数据集获取工具": "前置数据集获取工具", "占用中": "占用中", "去混响/去延迟，附：": "去混响/去延迟，附：", "参考音频在3~10秒范围外，请更换！": "参考音频在3~10秒范围外，请更换！", "参考音频的文本": "参考音频的文本", "参考音频的语种": "参考音频的语种", "句间停顿秒数": "句间停顿秒数", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。", "合成语音": "合成语音", "合成音频": "合成音频", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。", "后续将支持转音素、手工修改音素、语音合成分步执行。": "后续将支持转音素、手工修改音素、语音合成分步执行。", "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。": "听不清参考音频说的啥(不晓得写啥)可以开。开启后无视填写的参考文本。", "启用并行推理版本": "启用并行推理版本", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。", "多语种混合": "多语种混合", "多语种混合(粤语)": "多语种混合(粤语)", "失败": "失败", "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.", "实际输入的参考文本:": "实际输入的参考文本:", "实际输入的目标文本(切句后):": "实际输入的目标文本(切句后):", "实际输入的目标文本(每句):": "实际输入的目标文本(每句):", "实际输入的目标文本:": "实际输入的目标文本:", "导出文件格式": "导出文件格式", "已关闭": "已关闭", "已完成": "已完成", "已开启": "已开启", "并行合成中": "并行合成中", "并行推理": "并行推理", "并行推理模式已关闭": "并行推理模式已关闭", "并行推理模式已开启": "并行推理模式已开启", "底模缺失，无法加载相应 LoRA 权重": "底模缺失，无法加载相应 LoRA 权重", "开启": "开启", "开启无参考文本模式。不填参考文本亦相当于开启。": "开启无参考文本模式。不填参考文本亦相当于开启。", "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理": "当开启并行推理模式时，SoVits V3/4模型不支持分桶处理，已自动关闭分桶处理", "微调模型信息": "微调模型信息", "微调训练": "微调训练", "怎么切": "怎么切", "总训练轮数total_epoch": "总训练轮数total_epoch", "总训练轮数total_epoch，不建议太高": "总训练轮数total_epoch，不建议太高", "指定输出主人声文件夹": "指定输出主人声文件夹", "指定输出非主人声文件夹": "指定输出非主人声文件夹", "按中文句号。切": "按中文句号。切", "按标点符号切": "按标点符号切", "按英文句号.切": "按英文句号.切", "推理": "推理", "推理设置": "推理设置", "提取文本Bert特征": "提取文本Bert特征", "数据分桶(并行推理时会降低一点计算量)": "数据分桶(并行推理时会降低一点计算量)", "数据类型精度": "数据类型精度", "文本分词与特征提取": "文本分词与特征提取", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。", "文本模块学习率权重": "文本模块学习率权重", "施工中，请静候佳音": "施工中，请静候佳音", "日文": "日文", "日英混合": "日英混合", "是否仅保存最新的权重文件以节省硬盘空间": "是否仅保存最新的权重文件以节省硬盘空间", "是否在每次保存时间点将最终小模型保存至weights文件夹": "是否在每次保存时间点将最终小模型保存至weights文件夹", "是否开启DPO训练选项(实验性)": "是否开启DPO训练选项(实验性)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "是否直接对上次合成结果调整语速和音色。防止随机性。", "显卡信息": "显卡信息", "未下载模型": "未下载模型", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.": "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责.", "标注文件路径 (含文件后缀 *.list)": "标注文件路径 (含文件后缀 *.list)", "模型": "模型", "模型分为三类：": "模型分为三类：", "模型切换": "模型切换", "模型加载中，请等待": "模型加载中，请等待", "每张显卡的batch_size": "每张显卡的batch_size", "粤英混合": "粤英混合", "粤语": "粤语", "终止合成": "终止合成", "缺少Hubert数据集": "缺少Hubert数据集", "缺少语义数据集": "缺少语义数据集", "缺少音素数据集": "缺少音素数据集", "缺少音频数据集": "缺少音频数据集", "英文": "英文", "训练模型的版本": "训练模型的版本", "训练集格式化一键三连": "训练集格式化一键三连", "训练集格式化工具": "训练集格式化工具", "语义Token提取": "语义Token提取", "语速": "语速", "语速调整，高为更快": "语速调整，高为更快", "语速调节不支持分桶处理，已自动关闭分桶处理": "语速调节不支持分桶处理，已自动关闭分桶处理", "语音切分": "语音切分", "语音切分工具": "语音切分工具", "语音文本校对标注工具": "语音文本校对标注工具", "语音自监督特征提取": "语音自监督特征提取", "语音识别": "语音识别", "语音识别工具": "语音识别工具", "语音降噪": "语音降噪", "请上传3~10秒内参考音频，超过会报错！": "请上传3~10秒内参考音频，超过会报错！", "请上传参考音频": "请上传参考音频", "请填入推理文本": "请填入推理文本", "请填入正确的List路径": "请填入正确的List路径", "请填入正确的音频文件夹路径": "请填入正确的音频文件夹路径", "请输入有效文本": "请输入有效文本", "路径不存在,使用默认配置": "路径不存在,使用默认配置", "路径不能为空": "路径不能为空", "路径错误": "路径错误", "转换": "转换", "辅参考音频(可选多个，或不选)": "辅参考音频(可选多个，或不选)", "输入待处理音频文件夹路径": "输入待处理音频文件夹路径", "输入文件夹路径": "输入文件夹路径", "输入路径不存在": "输入路径不存在", "输入路径存在但不可用": "输入路径存在但不可用", "输出logs/实验名目录下应有23456开头的文件和文件夹": "输出logs/实验名目录下应有23456开头的文件和文件夹", "输出信息": "输出信息", "输出文件夹路径": "输出文件夹路径", "输出的语音": "输出的语音", "运行中": "运行中", "进度": "进度", "进程已终止": "进程已终止", "进程输出信息": "进程输出信息", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。": "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的几个是底模，体验5秒Zero Shot TTS不训练推理用。", "采样步数(仅对V3/4生效)": "采样步数(仅对V3/4生效)", "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试": "采样步数,如果觉得电,提高试试,如果觉得慢,降低试试", "重复惩罚": "重复惩罚", "随机种子": "随机种子", "需先终止才能开启下一次任务": "需先终止才能开启下一次任务", "需要合成的切分前文本": "需要合成的切分前文本", "需要合成的文本": "需要合成的文本", "需要合成的文本的语种": "需要合成的文本的语种", "需要合成的语种": "需要合成的语种", "韩文": "韩文", "韩英混合": "韩英混合", "音频加载失败": "音频加载失败", "音频文件不存在，跳过：": "音频文件不存在，跳过：", "音频标注WebUI": "音频标注WebUI", "音频自动切分输入路径，可文件可文件夹": "音频自动切分输入路径，可文件可文件夹", "音频超分中": "音频超分中", "音频超采样": "音频超采样", "音频超采样(仅对V3生效))": "音频超采样(仅对V3生效))", "预测语义Token": "预测语义Token", "预训练GPT模型路径": "预训练GPT模型路径", "预训练SSL模型路径": "预训练SSL模型路径", "预训练SoVITS-D模型路径": "预训练SoVITS-D模型路径", "预训练SoVITS-G模型路径": "预训练SoVITS-G模型路径", "预训练中文BERT模型路径": "预训练中文BERT模型路径", "预训练模型路径": "预训练模型路径"}