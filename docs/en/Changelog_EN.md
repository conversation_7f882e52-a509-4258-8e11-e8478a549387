# Changelog

## 202401

- 2024.01.21 [PR#108](https://github.com/RVC-Boss/GPT-SoVITS/pull/108)
  - Content: Added English system translation support to WebUI.
  - Type: Documentation
  - Contributor: D3lik
- 2024.01.21 [Commit#7b89c9ed](https://github.com/RVC-Boss/GPT-SoVITS/commit/7b89c9ed5669f63c4ed6ae791408969640bdcf3e)
  - Content: Attempted to fix SoVITS training ZeroDivisionError.
  - Type: Fix
  - Contributor: RVC-Boss, Tybost
  - Related: [Issue#79](https://github.com/RVC-Boss/GPT-SoVITS/issues/79)
- 2024.01.21 [Commit#ea62d6e0](https://github.com/RVC-Boss/GPT-SoVITS/commit/ea62d6e0cf1efd75287766ea2b55d1c3b69b4fd3)
  - Content: Significantly reduced the issue of synthesized audio containing the end of the reference audio.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.01.21 [Commit#a87ad522](https://github.com/RVC-Boss/GPT-SoVITS/commit/a87ad5228ed2d729da42019ae1b93171f6a745ef)
  - Content: `cmd-asr.py` now checks if the FunASR model is included in the default directory, and if not, it will download it from ModelScope.
  - Type: Feature
  - Contributor: RVC-Boss
- 2024.01.21 [Commit#f6147116](https://github.com/RVC-Boss/GPT-SoVITS/commit/f61471166c107ba56ccb7a5137fa9d7c09b2830d)
  - Content: `Config.py` now has an `is_share` parameter, which can be set to `True` to map the WebUI to the public network.
  - Type: Feature
  - Contributor: RVC-Boss
- 2024.01.21 [Commit#102d5081](https://github.com/RVC-Boss/GPT-SoVITS/commit/102d50819e5d24580d6e96085b636b25533ecc7f)
  - Content: Cleaned up cached audio files and other files in the `TEMP` folder.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.01.22 [Commit#872134c8](https://github.com/RVC-Boss/GPT-SoVITS/commit/872134c846bcb8f1909a3f5aff68a6aa67643f68)
  - Content: Fixed the issue where excessively short output files resulted in repeating the reference audio.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.01.22 Tested native support for English and Japanese training (Japanese training requires the root directory to be free of non-English special characters).
- 2024.01.22 [PR#124](https://github.com/RVC-Boss/GPT-SoVITS/pull/124)
  - Content: Improved audio path checking. If an attempt is made to read from an incorrect input path, it will report that the path does not exist instead of an ffmpeg error.
  - Type: Optimization
  - Contributor: xmimu
- 2024.01.23 [Commit#93c47cd9](https://github.com/RVC-Boss/GPT-SoVITS/commit/93c47cd9f0c53439536eada18879b4ec5a812ae1)
  - Content: Resolved the issue where Hubert extraction caused NaN errors, leading to SoVITS/GPT training ZeroDivisionError.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.01.23 [Commit#80fffb0a](https://github.com/RVC-Boss/GPT-SoVITS/commit/80fffb0ad46e4e7f27948d5a57c88cf342088d50)
  - Content: Replaced `jieba` with `jieba_fast` for Chinese word segmentation.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.01.23 [Commit#63625758](https://github.com/RVC-Boss/GPT-SoVITS/commit/63625758a99e645f3218dd167924e01a0e3cf0dc)
  - Content: Optimized model file sorting logic.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.01.23 [Commit#0c691191](https://github.com/RVC-Boss/GPT-SoVITS/commit/0c691191e894c15686e88279745712b3c6dc232f)
  - Content: Added support for quick model switching in the inference WebUI.
  - Type: Feature
  - Contributor: RVC-Boss
- 2024.01.25 [Commit#249561e5](https://github.com/RVC-Boss/GPT-SoVITS/commit/249561e5a18576010df6587c274d38cbd9e18b4b)
  - Content: Removed redundant logs in the inference WebUI.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.01.25 [PR#183](https://github.com/RVC-Boss/GPT-SoVITS/pull/183), [PR#200](https://github.com/RVC-Boss/GPT-SoVITS/pull/200)
  - Content: Supported training and inference on Mac.
  - Type: Feature
  - Contributor: Lion-Wu
- 2024.01.26 [Commit#813cf96e](https://github.com/RVC-Boss/GPT-SoVITS/commit/813cf96e508ba1bb2c658f38c7cc77b797fb4082), [Commit#2d1ddeca](https://github.com/RVC-Boss/GPT-SoVITS/commit/2d1ddeca42db90c3fe2d0cd79480fd544d87f02b)
  - Content: Fixed the issue of UVR5 reading and automatically jumping out of directories.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.01.26 [PR#204](https://github.com/RVC-Boss/GPT-SoVITS/pull/204)
  - Content: Added support for Chinese-English mixed and Japanese-English mixed output texts.
  - Type: Feature
  - Contributor: Kakaru Hayate
- 2024.01.26 [Commit#f4148cf7](https://github.com/RVC-Boss/GPT-SoVITS/commit/f4148cf77fb899c22bcdd4e773d2f24ab34a73e7)
  - Content: Added optional segmentation mode for output.
  - Type: Feature
  - Contributor: RVC-Boss
- 2024.01.26 [Commit#9fe955c1](https://github.com/RVC-Boss/GPT-SoVITS/commit/9fe955c1bf5f94546c9f699141281f2661c8a180)
  - Content: Fixed multiple newline issues causing inference errors.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.01.26 [Commit#84ee4719](https://github.com/RVC-Boss/GPT-SoVITS/commit/84ee471936b332bc2ccee024d6dfdedab4f0dc7b)
  - Content: Automatically forced single precision for GPU that do not support half precision; enforced single precision under CPU inference.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.01.28 [PR#238](https://github.com/RVC-Boss/GPT-SoVITS/pull/238)
  - Content: Completed model downloading process in the Dockerfile.
  - Type: Fix
  - Contributor: breakstring
- 2024.01.28 [PR#257](https://github.com/RVC-Boss/GPT-SoVITS/pull/257)
  - Content: Fixed the issue with the pronunciation of numbers converting to Chinese characters.
  - Type: Fix
  - Contributor: duliangang
- 2024.01.28 [Commit#f0cfe397](https://github.com/RVC-Boss/GPT-SoVITS/commit/f0cfe397089a6fd507d678c71adeaab5e7ed0683)
  - Content: Fixed the issue where GPT training did not save checkpoints.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.01.28 [Commit#b8ae5a27](https://github.com/RVC-Boss/GPT-SoVITS/commit/b8ae5a2761e2654fc0c905498009d3de9de745a8)
  - Content: Excluded unreasonable reference audio lengths by setting restrictions.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.01.28 [Commit#698e9655](https://github.com/RVC-Boss/GPT-SoVITS/commit/698e9655132d194b25b86fbbc99d53c8d2cea2a3)
  - Content: Fixed the issue where swallowing a few characters at the beginning of sentences.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.01.29 [Commit#ff977a5f](https://github.com/RVC-Boss/GPT-SoVITS/commit/ff977a5f5dc547e0ad82b9e0f1cd95fbc830b2b0)
  - Content: Changed training configurations to single precision for GPUs like the 16 series, which have issues with half precision training.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.01.29 [Commit#172e139f](https://github.com/RVC-Boss/GPT-SoVITS/commit/172e139f45ac26723bc2cf7fac0112f69d6b46ec)
  - Content: Tested and updated the available Colab version.
  - Type: Feature
  - Contributor: RVC-Boss
- 2024.01.29 [PR#135](https://github.com/RVC-Boss/GPT-SoVITS/pull/135)
  - Content: Updated FunASR to Version 1.0 and fixed errors caused by interface misalignment.
  - Type: Fix
  - Contributor: LauraGPT
- 2024.01.30 [Commit#1c2fa98c](https://github.com/RVC-Boss/GPT-SoVITS/commit/1c2fa98ca8c325dcfb32797d22ff1c2a726d1cb4)
  - Content: Fixed issues with splitting Chinese and English punctuation and added punctuation at the beginning and end of sentences.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.01.30 [Commit#74409f35](https://github.com/RVC-Boss/GPT-SoVITS/commit/74409f3570fa1c0ff28d4c65c288a6ce58ca00d2)
  - Content: Added splitting by punctuation.
  - Type: Feature
  - Contributor: RVC-Boss
- 2024.01.30 [Commit#c42eeccf](https://github.com/RVC-Boss/GPT-SoVITS/commit/c42eeccfdd2d0a0d714ecc8bfc22a12373aca6b7)
  - Content: Automatically removed double quotes from all path-related entries to prevent errors from novice users copying paths with double quotes.
  - Type: Fix
  - Contributor: RVC-Boss

## 202402

- 2024.02.01 [Commit#45f73519](https://github.com/RVC-Boss/GPT-SoVITS/commit/45f73519cc41cd17cf816d8b997a9dcb0bee04b6)
  - Content: Fixed the issue where an ASR path ending with `/` caused an error in saving the filename.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.02.03 [Commit#dba1a74c](https://github.com/RVC-Boss/GPT-SoVITS/commit/dba1a74ccb0cf19a1b4eb93faf11d4ec2b1fc5d7)
  - Content: Fixed the UVR5 format reading error causing separation failures.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.02.03 [Commit#3ebff70b](https://github.com/RVC-Boss/GPT-SoVITS/commit/3ebff70b71580ee1f97b3238c9442cbc5aef47c7)
  - Content: Supported automatic segmentation and language recognition for mixed Chinese-Japanese-English texts.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.02.03 [PR#377](https://github.com/RVC-Boss/GPT-SoVITS/pull/377)
  - Content: introduced PaddleSpeech's Normalizer to fix issues like reading "xx.xx%" (percent symbols) and "元/吨" being read as "元吨" instead of "元每吨", and fixed underscore errors.
  - Type: Optimization
  - Contributor: KamioRinn
- 2024.02.05 [PR#395](https://github.com/RVC-Boss/GPT-SoVITS/pull/395)
  - Content: Optimized English text frontend.
  - Type: Optimization
  - Contributor: KamioRinn
- 2024.02.06 [Commit#](https://github.com/RVC-Boss/GPT-SoVITS/commit/65b463a787f31637b4768cc9a47cab59541d3927)
  - Content: Corrected language parameter confusion causing decreased Chinese inference quality.
  - Type: Fix
  - Contributor: RVC-Boss
  - Related: [Issue#391](https://github.com/RVC-Boss/GPT-SoVITS/issues/391)
- 2024.02.06 [PR#403](https://github.com/RVC-Boss/GPT-SoVITS/pull/403)
  - Content: Adapted UVR5 to higher versions of librosa.
  - Type: Fix
  - Contributor: StaryLan
- 2024.02.07 [Commit#14a28510](https://github.com/RVC-Boss/GPT-SoVITS/commit/14a285109a521679f8846589c22da8f656a46ad8)
  - Content: Fixed UVR5 inf everywhere error caused by `is_half` parameter not converting to boolean, resulting in constant half precision inference, which caused `inf` on 16 series GPUs.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.02.07 [Commit#d74f888e](https://github.com/RVC-Boss/GPT-SoVITS/commit/d74f888e7ac86063bfeacef95d0e6ddafe42b3b2)
  - Content: Fixed Gradio dependencies.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.02.07 [PR#400](https://github.com/RVC-Boss/GPT-SoVITS/pull/400)
  - Content: Integrated Faster Whisper ASR for Japanese and English.
  - Type: Feature
  - Contributor: Shadow
- 2024.02.07 [Commit#6469048d](https://github.com/RVC-Boss/GPT-SoVITS/commit/6469048de12a8d6f0bd05d07f031309e61575a38)~[Commit#94ee71d9](https://github.com/RVC-Boss/GPT-SoVITS/commit/94ee71d9d562d10c9a1b96e745c6a6575aa66a10)
  - Content: Supported automatic reading of `.list` full paths if the root directory is left blank during dataset preparation.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.02.08 [Commit#59f35ada](https://github.com/RVC-Boss/GPT-SoVITS/commit/59f35adad85815df27e9c6b33d420f5ebfd8376b)
  - Content: Attempted to fix GPT training hang on Windows 10 1909 and Traditional Chinese System Language.
  - Type: Fix
  - Contributor: RVC-Boss
  - Related: [Issue#232](https://github.com/RVC-Boss/GPT-SoVITS/issues/232)
- 2024.02.12 [PR#457](https://github.com/RVC-Boss/GPT-SoVITS/pull/457)
  - Content: Enabled experimental DPO Loss training option to mitigate GPT repetition and missing characters by constructing negative samples during training and made several inference parameters available in the inference WebUI.
  - Type: Feature
  - Contributor: liufenghua
- 2024.02.12 [Commit#](https://github.com/RVC-Boss/GPT-SoVITS/commit/2fa74ecb941db27d9015583a9be6962898d66730), [Commit#](https://github.com/RVC-Boss/GPT-SoVITS/commit/d82f6bbb98ba725e6725dcee99b80ce71fb0bf28)
  - Content: Optimized logic for Faster Whisper and FunASR, switching Faster Whisper to mirror downloads to avoid issues with Hugging Face connections.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.02.15 [Commit#](https://github.com/RVC-Boss/GPT-SoVITS/commit/dd2c4d6d7121bf82d29d0f0e4d788f3b231997c8)
  - Content: Supported Chinese experiment names in training (previously caused errors).
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.02.15 [Commit#ccb9b08b](https://github.com/RVC-Boss/GPT-SoVITS/commit/ccb9b08be3c58e102defcc94ff4fd609da9e27ee)~[Commit#895fde46](https://github.com/RVC-Boss/GPT-SoVITS/commit/895fde46e420040ed26aaf0c5b7e99359d9b199b)
  - Content: Made DPO training an optional feature instead of mandatory. If selected, the batch size is automatically halved. Fixed issues with new parameters not being passed in the inference WebUI.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.02.15 [Commit#7b0c3c67](https://github.com/RVC-Boss/GPT-SoVITS/commit/7b0c3c676495c64b2064aa472bff14b5c06206a5)
  - Content: Fixed bugs in Chinese frontend.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.02.16 [PR#499](https://github.com/RVC-Boss/GPT-SoVITS/pull/499)
  - Content: Supported input without reference text.
  - Type: Feature
  - Contributor: Watchtower-Liu
  - Related: [Issue#475](https://github.com/RVC-Boss/GPT-SoVITS/issues/475)
- 2024.02.17 [PR#509](https://github.com/RVC-Boss/GPT-SoVITS/pull/509), [PR#507](https://github.com/RVC-Boss/GPT-SoVITS/pull/507), [PR#532](https://github.com/RVC-Boss/GPT-SoVITS/pull/532), [PR#556](https://github.com/RVC-Boss/GPT-SoVITS/pull/556), [PR#559](https://github.com/RVC-Boss/GPT-SoVITS/pull/559)
  - Content: Optimized Chinese and Japanese frontend processing.
  - Type: Optimization
  - Contributor: KamioRinn, v3cun
- 2024.02.17 [PR#510](https://github.com/RVC-Boss/GPT-SoVITS/pull/511), [PR#511](https://github.com/RVC-Boss/GPT-SoVITS/pull/511)
  - Content: Fixed Colab public URL issue.
  - Type: Fix
  - Contributor: ChanningWang2018, RVC-Boss
- 2024.02.21 [PR#557](https://github.com/RVC-Boss/GPT-SoVITS/pull/557)
  - Content: Switched Mac CPU inference to use CPU instead of MPS for faster performance.
  - Type: Optimization
  - Contributor: XXXXRT666
- 2024.02.21 [Commit#6da486c1](https://github.com/RVC-Boss/GPT-SoVITS/commit/6da486c15d09e3d99fa42c5e560aaac56b6b4ce1), [Commit#](https://github.com/RVC-Boss/GPT-SoVITS/commit/5a17177342d2df1e11369f2f4f58d34a3feb1a35)
  - Content: Added a noise reduction option during data processing (noise reduction leaves only 16kHz sampling rate; use only if the background noise is significant).
  - Type: Feature
  - Contributor: RVC-Boss
- 2024.02.28 [PR#573](https://github.com/RVC-Boss/GPT-SoVITS/pull/573)
  - Content: Modified `is_half` check to ensure proper CPU inference on Mac.
  - Type: Fix
  - Contributor: XXXXRT666
- 2024.02.28 [PR#610](https://github.com/RVC-Boss/GPT-SoVITS/pull/610)
  - Content: Fixed UVR5 reverb removal model where the setting was reversed.
  - Type: Fix
  - Contributor: Yuze Wang

## 202403

- 2024.03.06 [PR#675](https://github.com/RVC-Boss/GPT-SoVITS/pull/675)
  - Content: Enabled automatic CPU inference for Faster Whisper if no CUDA is available.
  - Type: Optimization
  - Contributor: ShiroDoMain
- 2024.03.06 [Commit#616be20d](https://github.com/RVC-Boss/GPT-SoVITS/commit/616be20db3cf94f1cd663782fea61b2370704193)
  - Content: No longer requires downloading the Chinese FunASR model first when using Faster Whisper non-Chinese ASR.
  - Type: Optimization
  - Contributor: RVC-Boss
- 2024.03.09 [PR#672](https://github.com/RVC-Boss/GPT-SoVITS/pull/672)
  - Content: Accelerated inference by 50% (tested on RTX3090 + PyTorch 2.2.1 + CU11.8 + Win10 + Py39).
  - Type: Optimization
  - Contributor: GoHomeToMacDonal
- 2024.03.10 [PR#721](https://github.com/RVC-Boss/GPT-SoVITS/pull/721)
  - Content: Added a quick inference branch `fast_inference_`.
  - Type: Feature
  - Contributor: ChasonJiang
- 2024.03.13 [PR#761](https://github.com/RVC-Boss/GPT-SoVITS/pull/761)
  - Content: Supported CPU training, using CPU for training on macOS.
  - Type: Feature
  - Contributor: Lion-Wu
- 2024.03.19 [PR#804](https://github.com/RVC-Boss/GPT-SoVITS/pull/804), [PR#812](https://github.com/RVC-Boss/GPT-SoVITS/pull/812), [PR#821](https://github.com/RVC-Boss/GPT-SoVITS/pull/821)
  - Content: Optimized the English text frontend.
  - Type: Optimization
  - Contributor: KamioRinn
- 2024.03.30 [PR#894](https://github.com/RVC-Boss/GPT-SoVITS/pull/894)
  - Content: Improved API format.
  - Type: Optimization
  - Contributor: KamioRinn

## 202404

- 2024.04.03 [PR#917](https://github.com/RVC-Boss/GPT-SoVITS/pull/917)
  - Content: Corrected FFmpeg command string formatting in UVR5 WebUI.
  - Type: Fix
  - Contributor: StaryLan

## 202405

- 2024.05.02 [PR#953](https://github.com/RVC-Boss/GPT-SoVITS/pull/953)
  - Content: Fixed the issue of SoVITS training without freezing VQ (which could cause quality degradation).
  - Type: Fix
  - Contributor: hcwu1993
  - Related: [Issue#747](https://github.com/RVC-Boss/GPT-SoVITS/issues/747)
- 2024.05.19 [PR#1102](https://github.com/RVC-Boss/GPT-SoVITS/pull/1102)
  - Content: Added error prompts for unsupported languages during training data processing.
  - Type: Optimization
  - Contributor: StaryLan
- 2024.05.27 [PR#1132](https://github.com/RVC-Boss/GPT-SoVITS/pull/1132)
  - Content: Fixed the bug in Hubert extraction.
  - Type: Fix
  - Contributor: XXXXRT666

## 202406

- 2024.06.06 [Commit#](https://github.com/RVC-Boss/GPT-SoVITS/commit/99f09c8bdc155c1f4272b511940717705509582a)
  - Content: Fixed the issue of WebUI's GPT fine-tuning not reading BERT feature of Chinese input texts, causing inconsistency with inference and potential quality degradation.
   **Caution: If you have previously fine-tuned with a large amount of data, it is recommended to retune the model to improve quality.**
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.06.07 [PR#1159](https://github.com/RVC-Boss/GPT-SoVITS/pull/1159)
  - Content: Fixed progress bar logic for SoVITS training in `s2_train.py`.
  - Type: Fix
  - Contributor: pengzhendong
- 2024.06.10 [Commit#501a74ae](https://github.com/RVC-Boss/GPT-SoVITS/commit/501a74ae96789a26b48932babed5eb4e9483a232)
  - Content: Fixed string formatting when UVR5 MDXNet calls FFmpeg, ensuring compatibility with paths containing spaces.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.06.10 [PR#1168](https://github.com/RVC-Boss/GPT-SoVITS/pull/1168), [PR#1169](https://github.com/RVC-Boss/GPT-SoVITS/pull/1169)
  - Content: Improved the logic for pure punctuation and multi-punctuation text input.
  - Type: Fix
  - Contributor: XXXXRT666
  - Related: [Issue#1165](https://github.com/RVC-Boss/GPT-SoVITS/issues/1165)
- 2024.06.13 [Commit#db506705](https://github.com/RVC-Boss/GPT-SoVITS/commit/db50670598f0236613eefa6f2d5a23a271d82041)
  - Content: Fixed default batch size decimal issue in CPU inference.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.06.28 [PR#1258](https://github.com/RVC-Boss/GPT-SoVITS/pull/1258), [PR#1265](https://github.com/RVC-Boss/GPT-SoVITS/pull/1265), [PR#1267](https://github.com/RVC-Boss/GPT-SoVITS/pull/1267)
  - Content: Fixed issues where denoising or ASR encountering exceptions would exit all pending audio files.
  - Type: Fix
  - Contributor: XXXXRT666
- 2024.06.29 [Commit#a208698e](https://github.com/RVC-Boss/GPT-SoVITS/commit/a208698e775155efc95b187b746d153d0f2847ca)
  - Content: Fixed multi-process save logic for multi-GPU training.
  - Type: Fix
  - Contributor: RVC-Boss
- 2024.06.29 [PR#1251](https://github.com/RVC-Boss/GPT-SoVITS/pull/1251)
  - Content: Removed redundant `my_utils.py`.
  - Type: Optimization
  - Contributor: aoguai
  - Related: [Issue#1189](https://github.com/RVC-Boss/GPT-SoVITS/issues/1189)

## 202407

- 2024.07.06 [PR#1253](https://github.com/RVC-Boss/GPT-SoVITS/pull/1253)
  - Content: Fixed the issue of splitting decimals when splitting by punctuation.
  - Type: Fix
  - Contributor: aoguai
- 2024.07.06 [Commit#](https://github.com/RVC-Boss/GPT-SoVITS/commit/b0786f2998f1b2fce6678434524b4e0e8cc716f5)
  - Content: The accelerated inference code has been validated and merged into the main branch, ensuring consistent inference effects with the base. It also supports accelerated inference in no-reference text mode.
  - Type: Optimization
  - Contributor: RVC-Boss, GoHomeToMacDonal
  - Related: [PR#672](https://github.com/RVC-Boss/GPT-SoVITS/pull/672)
- Future updates will continue to verify the consistency of changes in the `fast_inference` branch.

- 2024.07.13 [PR#1294](https://github.com/RVC-Boss/GPT-SoVITS/pull/1294), [PR#1298](https://github.com/RVC-Boss/GPT-SoVITS/pull/1298)
  - Content: Refactor i18n scanning and update multi-language configuration files.
  - Type: Documentation
  - Contributor: StaryLan
- 2024.07.13 [PR#1299](https://github.com/RVC-Boss/GPT-SoVITS/pull/1299)
  - Content: Fixed issues where trailing slashes in user file paths caused command line errors.
  - Type: Fix
  - Contributor: XXXXRT666
- 2024.07.19 [PR#756](https://github.com/RVC-Boss/GPT-SoVITS/pull/756)
  - Content: Fix the inconsistency in training steps when using a custom `bucket_sampler` during GPT training.
  - Type: Fix
  - Contributor: huangxu1991
- 2024.07.23 [Commit#9588a3c5](https://github.com/RVC-Boss/GPT-SoVITS/commit/9588a3c52d9ebdb20b3c5d74f647d12e7c1171c2), [PR#1340](https://github.com/RVC-Boss/GPT-SoVITS/pull/1340)
  - Content: Support adjusting speech speed during synthesis, including an option to freeze randomness and only control speed. This feature has been updated to `api.py`.
  - Type: Feature
  - Contributor: RVC-Boss, 红血球AE3803
- 2024.07.27 [PR#1306](https://github.com/RVC-Boss/GPT-SoVITS/pull/1306), [PR#1356](https://github.com/RVC-Boss/GPT-SoVITS/pull/1356)
  - Content: Added support for the BS-RoFormer vocal accompaniment separation model.
  - Type: Feature
  - Contributor: KamioRinn
- 2024.07.27 [PR#1351](https://github.com/RVC-Boss/GPT-SoVITS/pull/1351)
  - Content: Improved Chinese text frontend.
  - Type: Feature
  - Contributor: KamioRinn

## 202408 (V2 Version)

- 2024.08.01 [PR#1355](https://github.com/RVC-Boss/GPT-SoVITS/pull/1355)
  - Content: Automatically fill in the paths when processing files in the WebUI.
  - Type: Chore
  - Contributor: XXXXRT666
- 2024.08.01 [Commit#e62e9653](https://github.com/RVC-Boss/GPT-SoVITS/commit/e62e965323a60a76a025bcaa45268c1ddcbcf05c)
  - Content: Enabled FP16 inference support for BS-Roformer.
  - Type: Performance Optimization
  - Contributor: RVC-Boss
- 2024.08.01 [Commit#bce451a2](https://github.com/RVC-Boss/GPT-SoVITS/commit/bce451a2d1641e581e200297d01f219aeaaf7299), [Commit#4c8b7612](https://github.com/RVC-Boss/GPT-SoVITS/commit/4c8b7612206536b8b4435997acb69b25d93acb78)
  - Content: Optimized GPU recognition logic, added user-friendly logic to handle arbitrary GPU indices entered by users.
  - Type: Chore
  - Contributor: RVC-Boss
- 2024.08.02 [Commit#ff6c193f](https://github.com/RVC-Boss/GPT-SoVITS/commit/ff6c193f6fb99d44eea3648d82ebcee895860a22)~[Commit#de7ee7c7](https://github.com/RVC-Boss/GPT-SoVITS/commit/de7ee7c7c15a2ec137feb0693b4ff3db61fad758)
  - Content: **Added GPT-SoVITS V2 model.**
  - Type: Feature
  - Contributor: RVC-Boss
- 2024.08.03 [Commit#8a101474](https://github.com/RVC-Boss/GPT-SoVITS/commit/8a101474b5a4f913b4c94fca2e3ca87d0771bae3)
  - Content: Added support for Cantonese ASR by using FunASR.
  - Type: Feature
  - Contributor: RVC-Boss
- 2024.08.03 [PR#1387](https://github.com/RVC-Boss/GPT-SoVITS/pull/1387), [PR#1388](https://github.com/RVC-Boss/GPT-SoVITS/pull/1388)
  - Content: Optimized UI and timing logic.
  - Type: Chore
  - Contributor: XXXXRT666
- 2024.08.06 [PR#1404](https://github.com/RVC-Boss/GPT-SoVITS/pull/1404), [PR#987](https://github.com/RVC-Boss/GPT-SoVITS/pull/987), [PR#488](https://github.com/RVC-Boss/GPT-SoVITS/pull/488)
  - Content: Optimized polyphonic character handling logic (V2 Only).
  - Type: Fix, Feature
  - Contributor: KamioRinn, RVC-Boss
- 2024.08.13 [PR#1422](https://github.com/RVC-Boss/GPT-SoVITS/pull/1422)
  - Content: Fixed bug where only one reference audio could be uploaded; added dataset validation with warning popups for missing files.
  - Type: Fix, Chore
  - Contributor: XXXXRT666
- 2024.08.20 [Issue#1508](https://github.com/RVC-Boss/GPT-SoVITS/issues/1508)
  - Content: Upstream LangSegment library now supports optimizing numbers, phone numbers, dates, and times using SSML tags.
  - Type: Feature
  - Contributor: juntaosun
- 2024.08.20 [PR#1503](https://github.com/RVC-Boss/GPT-SoVITS/pull/1503)
  - Content: Fixed and optimized API.
  - Type: Fix
  - Contributor: KamioRinn
- 2024.08.20 [PR#1490](https://github.com/RVC-Boss/GPT-SoVITS/pull/1490)
  - Content: Merged `fast_inference` branch into the main branch.
  - Type: Refactor
  - Contributor: ChasonJiang
- 2024.08.21 **Officially released GPT-SoVITS V2 version.**

## 202502 (V3 Version)

- 2025.02.11 [Commit#ed207c4b](https://github.com/RVC-Boss/GPT-SoVITS/commit/ed207c4b879d5296e9be3ae5f7b876729a2c43b8)~[Commit#6e2b4918](https://github.com/RVC-Boss/GPT-SoVITS/commit/6e2b49186c5b961f0de41ea485d398dffa9787b4)
  - Content: **Added GPT-SoVITS V3 model, which requires 14GB VRAM for fine-tuning.**
  - Type: Feature (Refer to [Wiki](https://github.com/RVC-Boss/GPT-SoVITS/wiki/GPT%E2%80%90SoVITS%E2%80%90v3%E2%80%90features-(%E6%96%B0%E7%89%B9%E6%80%A7)))
  - Contributor: RVC-Boss
- 2025.02.12 [PR#2032](https://github.com/RVC-Boss/GPT-SoVITS/pull/2032)
  - Content: Updated multilingual project documentation.
  - Type: Documentation
  - Contributor: StaryLan
- 2025.02.12 [PR#2033](https://github.com/RVC-Boss/GPT-SoVITS/pull/2033)
  - Content: Updated Japanese documentation.
  - Type: Documentation
  - Contributor: Fyphen
- 2025.02.12 [PR#2010](https://github.com/RVC-Boss/GPT-SoVITS/pull/2010)
  - Content: Optimized attention calculation logic.
  - Type: Performance Optimization
  - Contributor: wzy3650
- 2025.02.12 [PR#2040](https://github.com/RVC-Boss/GPT-SoVITS/pull/2040)
  - Content: Added gradient checkpointing support for fine-tuning, requiring 12GB VRAM.
  - Type: Feature
  - Contributor: Kakaru Hayate
- 2025.02.14 [PR#2047](https://github.com/RVC-Boss/GPT-SoVITS/pull/2047), [PR#2062](https://github.com/RVC-Boss/GPT-SoVITS/pull/2062), [PR#2073](https://github.com/RVC-Boss/GPT-SoVITS/pull/2073)
  - Content: Switched to a new language segmentation tool, improved multilingual mixed-text splitting strategy, and optimized number and English processing logic.
  - Type: Feature
  - Contributor: KamioRinn
- 2025.02.23 [Commit#56509a17](https://github.com/RVC-Boss/GPT-SoVITS/commit/56509a17c918c8d149c48413a672b8ddf437495b)~[Commit#514fb692](https://github.com/RVC-Boss/GPT-SoVITS/commit/514fb692db056a06ed012bc3a5bca2a5b455703e)
  - Content: **GPT-SoVITS V3 model now supports LoRA training, requiring 8GB GPU Memory for fine-tuning.**
  - Type: Feature
  - Contributor: RVC-Boss
- 2025.02.23 [PR#2078](https://github.com/RVC-Boss/GPT-SoVITS/pull/2078)
  - Content: Added Mel Band Roformer model support for vocal and Instrument separation.
  - Type: Feature
  - Contributor: Sucial
- 2025.02.26 [PR#2112](https://github.com/RVC-Boss/GPT-SoVITS/pull/2112), [PR#2114](https://github.com/RVC-Boss/GPT-SoVITS/pull/2114)
  - Content: Fixed MeCab error under Chinese paths (specifically for Japanese/Korean or multilingual text splitting).
  - Type: Fix
  - Contributor: KamioRinn
- 2025.02.27 [Commit#92961c3f](https://github.com/RVC-Boss/GPT-SoVITS/commit/92961c3f68b96009ff2cd00ce614a11b6c4d026f)~[Commit#250b1c73](https://github.com/RVC-Boss/GPT-SoVITS/commit/250b1c73cba60db18148b21ec5fbce01fd9d19bc)
  - Content: **Added 24kHz to 48kHz audio super-resolution models** to alleviate the "muffled" audio issue when generating 24K audio with V3 model.
  - Type: Feature
  - Contributor: RVC-Boss
  - Related: [Issue#2085](https://github.com/RVC-Boss/GPT-SoVITS/issues/2085), [Issue#2117](https://github.com/RVC-Boss/GPT-SoVITS/issues/2117)
- 2025.02.28 [PR#2123](https://github.com/RVC-Boss/GPT-SoVITS/pull/2123)
  - Content: Updated multilingual project documentation.
  - Type: Documentation
  - Contributor: StaryLan
- 2025.02.28 [PR#2122](https://github.com/RVC-Boss/GPT-SoVITS/pull/2122)
  - Content: Applied rule-based detection for short CJK characters when model cannot identify them.
  - Type: Fix
  - Contributor: KamioRinn
  - Related: [Issue#2116](https://github.com/RVC-Boss/GPT-SoVITS/issues/2116)
- 2025.02.28 [Commit#c38b1690](https://github.com/RVC-Boss/GPT-SoVITS/commit/c38b16901978c1db79491e16905ea3a37a7cf686), [Commit#a32a2b89](https://github.com/RVC-Boss/GPT-SoVITS/commit/a32a2b893436fad56cc82409121c7fa36a1815d5)
  - Content: Added speech rate parameter to control synthesis speed.
  - Type: Fix
  - Contributor: RVC-Boss
- 2025.02.28 **Officially released GPT-SoVITS V3**.

## 202503

- 2025.03.31 [PR#2236](https://github.com/RVC-Boss/GPT-SoVITS/pull/2236)
  - Content: Fixed issues caused by incorrect versions of dependencies.
  - Type: Fix
  - Contributor: XXXXRT666
  - Related:
    - PyOpenJTalk: [Issue#1131](https://github.com/RVC-Boss/GPT-SoVITS/issues/1131), [Issue#2231](https://github.com/RVC-Boss/GPT-SoVITS/issues/2231), [Issue#2233](https://github.com/RVC-Boss/GPT-SoVITS/issues/2233).
    - ONNX: [Issue#492](https://github.com/RVC-Boss/GPT-SoVITS/issues/492), [Issue#671](https://github.com/RVC-Boss/GPT-SoVITS/issues/671), [Issue#1192](https://github.com/RVC-Boss/GPT-SoVITS/issues/1192), [Issue#1819](https://github.com/RVC-Boss/GPT-SoVITS/issues/1819), [Issue#1841](https://github.com/RVC-Boss/GPT-SoVITS/issues/1841).
    - Pydantic: [Issue#2230](https://github.com/RVC-Boss/GPT-SoVITS/issues/2230), [Issue#2239](https://github.com/RVC-Boss/GPT-SoVITS/issues/2239).
    - PyTorch-Lightning: [Issue#2174](https://github.com/RVC-Boss/GPT-SoVITS/issues/2174).
- 2025.03.31 [PR#2241](https://github.com/RVC-Boss/GPT-SoVITS/pull/2241)
  - Content: **Enabled parallel inference for SoVITS v3.**
  - Type: Feature
  - Contributor: ChasonJiang

- Fixed other minor bugs.

- Integrated package fixes for ONNX runtime GPU inference support:
  - Type: Fix
  - Details:
    - ONNX models within G2PW switched from CPU to GPU inference, significantly reducing CPU bottleneck;
    - foxjoy dereverberation model now supports GPU inference.

## 202504 (V4 Version)

- 2025.04.01 [Commit#6a60e5ed](https://github.com/RVC-Boss/GPT-SoVITS/commit/6a60e5edb1817af4a61c7a5b196c0d0f1407668f)
  - Content: Unlocked SoVITS v3 parallel inference; fixed asynchronous model loading logic.
  - Type: Fix
  - Contributor: RVC-Boss
- 2025.04.07 [PR#2255](https://github.com/RVC-Boss/GPT-SoVITS/pull/2255)
  - Content: Code formatting using Ruff; updated G2PW link.
  - Type: Style
  - Contributor: XXXXRT666
- 2025.04.15 [PR#2290](https://github.com/RVC-Boss/GPT-SoVITS/pull/2290)
  - Content: Cleaned up documentation; added Python 3.11 support; updated installers.
  - Type: Chore
  - Contributor: XXXXRT666
- 2025.04.20 [PR#2300](https://github.com/RVC-Boss/GPT-SoVITS/pull/2300)
  - Content: Updated Colab, installation files, and model downloads.
  - Type: Chore
  - Contributor: XXXXRT666
- 2025.04.20 [Commit#e0c452f0](https://github.com/RVC-Boss/GPT-SoVITS/commit/e0c452f0078e8f7eb560b79a54d75573fefa8355)~[Commit#9d481da6](https://github.com/RVC-Boss/GPT-SoVITS/commit/9d481da610aa4b0ef8abf5651fd62800d2b4e8bf)
  - Content: **Added GPT-SoVITS V4 model.**
  - Type: Feature
  - Contributor: RVC-Boss
- 2025.04.21 [Commit#8b394a15](https://github.com/RVC-Boss/GPT-SoVITS/commit/8b394a15bce8e1d85c0b11172442dbe7a6017ca2)~[Commit#bc2fe5ec](https://github.com/RVC-Boss/GPT-SoVITS/commit/bc2fe5ec86536c77bb3794b4be263ac87e4fdae6), [PR#2307](https://github.com/RVC-Boss/GPT-SoVITS/pull/2307)
  - Content: Enabled parallel inference for V4.
  - Type: Feature
  - Contributor: RVC-Boss, ChasonJiang
- 2025.04.22 [Commit#7405427a](https://github.com/RVC-Boss/GPT-SoVITS/commit/7405427a0ab2a43af63205df401fd6607a408d87)~[Commit#590c83d7](https://github.com/RVC-Boss/GPT-SoVITS/commit/590c83d7667c8d4908f5bdaf2f4c1ba8959d29ff), [PR#2309](https://github.com/RVC-Boss/GPT-SoVITS/pull/2309)
  - Content: Fixed model version parameter passing.
  - Type: Fix
  - Contributor: RVC-Boss, ChasonJiang
- 2025.04.22 [Commit#fbdab94e](https://github.com/RVC-Boss/GPT-SoVITS/commit/fbdab94e17d605d85841af6f94f40a45976dd1d9), [PR#2310](https://github.com/RVC-Boss/GPT-SoVITS/pull/2310)
  - Content: Fixed Numpy and Numba version mismatch issue; updated librosa version.
  - Type: Fix
  - Contributor: RVC-Boss, XXXXRT666
  - Related: [Issue#2308](https://github.com/RVC-Boss/GPT-SoVITS/issues/2308)
- **2024.04.22 Officially released GPT-SoVITS V4**.
- 2025.04.22 [PR#2311](https://github.com/RVC-Boss/GPT-SoVITS/pull/2311)
  - Content: Updated Gradio parameters.
  - Type: Chore
  - Contributor: XXXXRT666
- 2025.04.25 [PR#2322](https://github.com/RVC-Boss/GPT-SoVITS/pull/2322)
  - Content: Improved Colab/Kaggle notebook scripts.
  - Type: Chore
  - Contributor: XXXXRT666

## 202505

- 2025.05.26 [PR#2351](https://github.com/RVC-Boss/GPT-SoVITS/pull/2351)
  - Content: Improved Docker and Windows auto-build scripts; added pre-commit formatting.
  - Type: Chore
  - Contributor: XXXXRT666
- 2025.05.26 [PR#2408](https://github.com/RVC-Boss/GPT-SoVITS/pull/2408)
  - Content: Optimized multilingual text splitting and recognition logic.
  - Type: Fix
  - Contributor: KamioRinn
  - Related: [Issue#2404](https://github.com/RVC-Boss/GPT-SoVITS/issues/2404)
- 2025.05.26 [PR#2377](https://github.com/RVC-Boss/GPT-SoVITS/pull/2377)
  - Content: Implemented caching strategies to improve SoVITS V3/V4 inference speed by 10%.
  - Type: Performance Optimization
  - Contributor: Kakaru Hayate
- 2025.05.26 [Commit#4d9d56b1](https://github.com/RVC-Boss/GPT-SoVITS/commit/4d9d56b19638dc434d6eefd9545e4d8639a3e072), [Commit#8c705784](https://github.com/RVC-Boss/GPT-SoVITS/commit/8c705784c50bf438c7b6d0be33a9e5e3cb90e6b2), [Commit#fafe4e7f](https://github.com/RVC-Boss/GPT-SoVITS/commit/fafe4e7f120fba56c5f053c6db30aa675d5951ba)
  - Content: Updated the annotation interface with a reminder: click Submit Text after completing each page, or changes will not be saved.
  - Type: Fix
  - Contributor: RVC-Boss
- 2025.05.29 [Commit#1934fc1e](https://github.com/RVC-Boss/GPT-SoVITS/commit/1934fc1e1b22c4c162bba1bbe7d7ebb132944cdc)
  - Content: Fixed UVR5 and ONNX dereverberation model errors when FFmpeg encodes MP3/M4A files with spaces in original paths.
  - Type: Fix
  - Contributor: RVC-Boss

## 202506 (V2Pro Series)

- 2025.06.03 [PR#2420](https://github.com/RVC-Boss/GPT-SoVITS/pull/2420)
  - Content: Updated multilingual project documentation.
  - Type: Documentation
  - Contributor: StaryLan
- 2025.06.04 [PR#2417](https://github.com/RVC-Boss/GPT-SoVITS/pull/2417)
  - Content: Support exporting V4 with TorchScript.
  - Type: Feature
  - Contributor: L-jasmine
- 2025.06.04 [Commit#b7c0c5ca](https://github.com/RVC-Boss/GPT-SoVITS/commit/b7c0c5ca878bcdd419fd86bf80dba431a6653356)~[Commit#298ebb03](https://github.com/RVC-Boss/GPT-SoVITS/commit/298ebb03c5a719388527ae6a586c7ea960344e70)
  - Content: **Added GPT-SoVITS V2Pro Series model (V2Pro, V2ProPlus).**.
  - Type: Feature
  - Contributor: RVC-Boss
- 2025.06.05 [PR#2426](https://github.com/RVC-Boss/GPT-SoVITS/pull/2426)
  - Description: Fix initialization error in `config/inference_webui`.
  - Type: Fix
  - Contributor: StaryLan
- 2025.06.05 [PR#2427](https://github.com/RVC-Boss/GPT-SoVITS/pull/2427), [Commit#7d70852a](https://github.com/RVC-Boss/GPT-SoVITS/commit/7d70852a3f67c3b52e3a62857f8663d529efc8cd), [PR#2434](https://github.com/RVC-Boss/GPT-SoVITS/pull/2434)
  - Content: Optimized automatic precision detection logic; added collapsible functionality to WebUI frontend modules.
  - Type: New Feature
  - Contributors: XXXXRT666, RVC-Boss
