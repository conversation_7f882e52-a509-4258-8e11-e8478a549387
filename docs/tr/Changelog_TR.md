# Güncelleme Günlüğü

## 202401

## 202401

- 2024.01.21 [PR#108](https://github.com/RVC-Boss/GPT-SoVITS/pull/108)
  - İçerik: WebUI'ya İngilizce sistem çeviri desteği eklendi.
  - Tür: Dokümantasyon
  - Katkıda Bulunan: D3lik
- 2024.01.21 [Commit#7b89c9ed](https://github.com/RVC-Boss/GPT-SoVITS/commit/7b89c9ed5669f63c4ed6ae791408969640bdcf3e)
  - İçerik: SoVITS eğitiminde ZeroDivisionError düzeltme girişimi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss, Tybost
  - İlgili: [Issue#79](https://github.com/RVC-Boss/GPT-SoVITS/issues/79)
- 2024.01.21 [Commit#ea62d6e0](https://github.com/RVC-Boss/GPT-SoVITS/commit/ea62d6e0cf1efd75287766ea2b55d1c3b69b4fd3)
  - İçerik: Sentezlenen sesin referans sesin sonunu içerme sorunu önemli ölçüde azaltıldı.
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.01.21 [Commit#a87ad522](https://github.com/RVC-Boss/GPT-SoVITS/commit/a87ad5228ed2d729da42019ae1b93171f6a745ef)
  - İçerik: `cmd-asr.py` artık FunASR modelinin varsayılan dizinde olup olmadığını kontrol ediyor ve değilse ModelScope'tan indiriyor.
  - Tür: Özellik
  - Katkıda Bulunan: RVC-Boss
- 2024.01.21 [Commit#f6147116](https://github.com/RVC-Boss/GPT-SoVITS/commit/f61471166c107ba56ccb7a5137fa9d7c09b2830d)
  - İçerik: `Config.py`'a `is_share` parametresi eklendi, `True` olarak ayarlanırsa WebUI genel ağa eşlenir.
  - Tür: Özellik
  - Katkıda Bulunan: RVC-Boss
- 2024.01.21 [Commit#102d5081](https://github.com/RVC-Boss/GPT-SoVITS/commit/102d50819e5d24580d6e96085b636b25533ecc7f)
  - İçerik: `TEMP` klasöründeki önbelleğe alınmış ses dosyaları ve diğer dosyalar temizlendi.
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.01.22 [Commit#872134c8](https://github.com/RVC-Boss/GPT-SoVITS/commit/872134c846bcb8f1909a3f5aff68a6aa67643f68)
  - İçerik: Aşırı kısa çıktı dosyalarının referans sesi tekrarlaması sorunu düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.01.22 İngilizce ve Japonca eğitim için yerel destek test edildi (Japonca eğitim için kök dizinde İngilizce olmayan özel karakterler olmamalı).
- 2024.01.22 [PR#124](https://github.com/RVC-Boss/GPT-SoVITS/pull/124)
  - İçerik: Ses yolu kontrolü iyileştirildi. Yanlış giriş yolundan okuma girişiminde ffmpeg hatası yerine yolun mevcut olmadığı bildiriliyor.
  - Tür: Optimizasyon
  - Katkıda Bulunan: xmimu
- 2024.01.23 [Commit#93c47cd9](https://github.com/RVC-Boss/GPT-SoVITS/commit/93c47cd9f0c53439536eada18879b4ec5a812ae1)
  - İçerik: Hubert çıkarımının NaN hatalarına neden olarak SoVITS/GPT eğitiminde ZeroDivisionError'a yol açması sorunu çözüldü.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.01.23 [Commit#80fffb0a](https://github.com/RVC-Boss/GPT-SoVITS/commit/80fffb0ad46e4e7f27948d5a57c88cf342088d50)
  - İçerik: Çince kelime bölme için `jieba`, `jieba_fast` ile değiştirildi.
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.01.23 [Commit#63625758](https://github.com/RVC-Boss/GPT-SoVITS/commit/63625758a99e645f3218dd167924e01a0e3cf0dc)
  - İçerik: Model dosyası sıralama mantığı optimize edildi.
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.01.23 [Commit#0c691191](https://github.com/RVC-Boss/GPT-SoVITS/commit/0c691191e894c15686e88279745712b3c6dc232f)
  - İçerik: Çıkarım WebUI'ında hızlı model değiştirme desteği eklendi.
  - Tür: Özellik
  - Katkıda Bulunan: RVC-Boss
- 2024.01.25 [Commit#249561e5](https://github.com/RVC-Boss/GPT-SoVITS/commit/249561e5a18576010df6587c274d38cbd9e18b4b)
  - İçerik: Çıkarım WebUI'ında gereksiz günlükler kaldırıldı.
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.01.25 [PR#183](https://github.com/RVC-Boss/GPT-SoVITS/pull/183), [PR#200](https://github.com/RVC-Boss/GPT-SoVITS/pull/200)
  - İçerik: Mac'te eğitim ve çıkarım desteği eklendi.
  - Tür: Özellik
  - Katkıda Bulunan: Lion-Wu
- 2024.01.26 [Commit#813cf96e](https://github.com/RVC-Boss/GPT-SoVITS/commit/813cf96e508ba1bb2c658f38c7cc77b797fb4082), [Commit#2d1ddeca](https://github.com/RVC-Boss/GPT-SoVITS/commit/2d1ddeca42db90c3fe2d0cd79480fd544d87f02b)
  - İçerik: UVR5'in dizinleri okuyup otomatik olarak çıkması sorunu düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.01.26 [PR#204](https://github.com/RVC-Boss/GPT-SoVITS/pull/204)
  - İçerik: Çince-İngilizce ve Japonca-İngilizce karışık çıktı metinleri için destek eklendi.
  - Tür: Özellik
  - Katkıda Bulunan: Kakaru Hayate
- 2024.01.26 [Commit#f4148cf7](https://github.com/RVC-Boss/GPT-SoVITS/commit/f4148cf77fb899c22bcdd4e773d2f24ab34a73e7)
  - İçerik: Çıktı için isteğe bağlı bölümleme modu eklendi.
  - Tür: Özellik
  - Katkıda Bulunan: RVC-Boss
- 2024.01.26 [Commit#9fe955c1](https://github.com/RVC-Boss/GPT-SoVITS/commit/9fe955c1bf5f94546c9f699141281f2661c8a180)
  - İçerik: Birden fazla satır sonunun çıkarım hatasına neden olması sorunu düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.01.26 [Commit#84ee4719](https://github.com/RVC-Boss/GPT-SoVITS/commit/84ee471936b332bc2ccee024d6dfdedab4f0dc7b)
  - İçerik: Yarım hassasiyeti desteklemeyen GPU'lar için otomatik olarak tek hassasiyet zorlandı; CPU çıkarımında tek hassasiyet zorunlu kılındı.
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.01.28 [PR#238](https://github.com/RVC-Boss/GPT-SoVITS/pull/238)
  - İçerik: Dockerfile'da model indirme süreci tamamlandı.
  - Tür: Düzeltme
  - Katkıda Bulunan: breakstring
- 2024.01.28 [PR#257](https://github.com/RVC-Boss/GPT-SoVITS/pull/257)
  - İçerik: Sayıların telaffuzunun Çince karakterlere dönüşmesi sorunu düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: duliangang
- 2024.01.28 [Commit#f0cfe397](https://github.com/RVC-Boss/GPT-SoVITS/commit/f0cfe397089a6fd507d678c71adeaab5e7ed0683)
  - İçerik: GPT eğitiminde kontrol noktalarının kaydedilmemesi sorunu düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.01.28 [Commit#b8ae5a27](https://github.com/RVC-Boss/GPT-SoVITS/commit/b8ae5a2761e2654fc0c905498009d3de9de745a8)
  - İçerik: Kısıtlamalar ayarlanarak mantıksız referans ses uzunlukları hariç tutuldu.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.01.28 [Commit#698e9655](https://github.com/RVC-Boss/GPT-SoVITS/commit/698e9655132d194b25b86fbbc99d53c8d2cea2a3)
  - İçerik: Cümlelerin başında birkaç karakterin yutulması sorunu düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.01.29 [Commit#ff977a5f](https://github.com/RVC-Boss/GPT-SoVITS/commit/ff977a5f5dc547e0ad82b9e0f1cd95fbc830b2b0)
  - İçerik: 16 serisi gibi yarım hassasiyet eğitiminde sorun yaşayan GPU'lar için eğitim yapılandırmaları tek hassasiyete değiştirildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.01.29 [Commit#172e139f](https://github.com/RVC-Boss/GPT-SoVITS/commit/172e139f45ac26723bc2cf7fac0112f69d6b46ec)
  - İçerik: Kullanılabilir Colab sürümü test edildi ve güncellendi.
  - Tür: Özellik
  - Katkıda Bulunan: RVC-Boss
- 2024.01.29 [PR#135](https://github.com/RVC-Boss/GPT-SoVITS/pull/135)
  - İçerik: FunASR Sürüm 1.0'a güncellendi ve arayüz uyumsuzluğundan kaynaklanan hatalar düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: LauraGPT
- 2024.01.30 [Commit#1c2fa98c](https://github.com/RVC-Boss/GPT-SoVITS/commit/1c2fa98ca8c325dcfb32797d22ff1c2a726d1cb4)
  - İçerik: Çince ve İngilizce noktalama işaretlerinin bölünmesi sorunları düzeltildi ve cümle başlarına ve sonlarına noktalama işaretleri eklendi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.01.30 [Commit#74409f35](https://github.com/RVC-Boss/GPT-SoVITS/commit/74409f3570fa1c0ff28d4c65c288a6ce58ca00d2)
  - İçerik: Noktalama işaretlerine göre bölme desteği eklendi.
  - Tür: Özellik
  - Katkıda Bulunan: RVC-Boss
- 2024.01.30 [Commit#c42eeccf](https://github.com/RVC-Boss/GPT-SoVITS/commit/c42eeccfdd2d0a0d714ecc8bfc22a12373aca6b7)
  - İçerik: Yeni kullanıcıların yolları çift tırnak içinde kopyalayarak hata yapmasını önlemek için tüm yol ile ilgili girdilerden çift tırnaklar otomatik olarak kaldırıldı.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss

## 202402

- 2024.02.01 [Commit#45f73519](https://github.com/RVC-Boss/GPT-SoVITS/commit/45f73519cc41cd17cf816d8b997a9dcb0bee04b6)
  - İçerik: ASR yolunun `/` ile bitmesi durumunda dosya adı kaydetme hatası düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.02.03 [Commit#dba1a74c](https://github.com/RVC-Boss/GPT-SoVITS/commit/dba1a74ccb0cf19a1b4eb93faf11d4ec2b1fc5d7)
  - İçerik: UVR5 format okuma hatası nedeniyle ayrıştırma başarısızlığı sorunu çözüldü.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.02.03 [Commit#3ebff70b](https://github.com/RVC-Boss/GPT-SoVITS/commit/3ebff70b71580ee1f97b3238c9442cbc5aef47c7)
  - İçerik: Çince-Japonca-İngilizce karışık metinler için otomatik bölümleme ve dil tanıma desteği eklendi.
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.02.03 [PR#377](https://github.com/RVC-Boss/GPT-SoVITS/pull/377)
  - İçerik: PaddleSpeech Normalizer entegre edildi - "xx.xx%" (yüzde sembolü) ve "元/吨" ifadelerinin "元吨" yerine "元每吨" olarak okunması sorunu ile alt çizgi hataları düzeltildi.
  - Tür: Optimizasyon
  - Katkıda Bulunan: KamioRinn
- 2024.02.05 [PR#395](https://github.com/RVC-Boss/GPT-SoVITS/pull/395)
  - İçerik: İngilizce metin ön uç işleme optimizasyonu yapıldı.
  - Tür: Optimizasyon
  - Katkıda Bulunan: KamioRinn
- 2024.02.06 [Commit#65b463a7](https://github.com/RVC-Boss/GPT-SoVITS/commit/65b463a787f31637b4768cc9a47cab59541d3927)
  - İçerik: Dil parametrelerinin karışması nedeniyle Çince çıkarım kalitesinin düşmesi sorunu giderildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
  - İlgili: [Issue#391](https://github.com/RVC-Boss/GPT-SoVITS/issues/391)
- 2024.02.06 [PR#403](https://github.com/RVC-Boss/GPT-SoVITS/pull/403)
  - İçerik: UVR5, librosa'nın daha yeni sürümlerine uyumlu hale getirildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: StaryLan
- 2024.02.07 [Commit#14a28510](https://github.com/RVC-Boss/GPT-SoVITS/commit/14a285109a521679f8846589c22da8f656a46ad8)
  - İçerik: `is_half` parametresinin boolean'a dönüştürülmemesi nedeniyle oluşan UVR5 inf hatası düzeltildi (16 serisi GPU'larda `inf` sorununa neden oluyordu).
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.02.07 [Commit#d74f888e](https://github.com/RVC-Boss/GPT-SoVITS/commit/d74f888e7ac86063bfeacef95d0e6ddafe42b3b2)
  - İçerik: Gradio bağımlılık sorunları giderildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.02.07 [PR#400](https://github.com/RVC-Boss/GPT-SoVITS/pull/400)
  - İçerik: Japonca ve İngilizce için Faster Whisper ASR entegrasyonu yapıldı.
  - Tür: Özellik
  - Katkıda Bulunan: Shadow
- 2024.02.07 [Commit#6469048d](https://github.com/RVC-Boss/GPT-SoVITS/commit/6469048de12a8d6f0bd05d07f031309e61575a38)~[Commit#94ee71d9](https://github.com/RVC-Boss/GPT-SoVITS/commit/94ee71d9d562d10c9a1b96e745c6a6575aa66a10)
  - İçerik: Veri seti hazırlarken kök dizin boş bırakılırsa `.list` dosya yollarının otomatik okunması desteği eklendi.
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.02.08 [Commit#59f35ada](https://github.com/RVC-Boss/GPT-SoVITS/commit/59f35adad85815df27e9c6b33d420f5ebfd8376b)
  - İçerik: Windows 10 1909 ve Geleneksel Çince sistem dilinde GPT eğitiminin donma sorunu çözülmeye çalışıldı.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
  - İlgili: [Issue#232](https://github.com/RVC-Boss/GPT-SoVITS/issues/232)
- 2024.02.12 [PR#457](https://github.com/RVC-Boss/GPT-SoVITS/pull/457)
  - İçerik: DPO Loss eğitim seçeneği eklendi (GPT tekrarlarını ve karakter atlamalarını azaltmak için), çıkarım WebUI'sına yeni parametreler eklendi.
  - Tür: Özellik
  - Katkıda Bulunan: liufenghua
- 2024.02.12 [Commit#2fa74ecb](https://github.com/RVC-Boss/GPT-SoVITS/commit/2fa74ecb941db27d9015583a9be6962898d66730), [Commit#d82f6bbb](https://github.com/RVC-Boss/GPT-SoVITS/commit/d82f6bbb98ba725e6725dcee99b80ce71fb0bf28)
  - İçerik: Faster Whisper ve FunASR mantığı optimize edildi, Hugging Face bağlantı sorunlarını önlemek için yansı indirmelere geçildi.
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.02.15 [Commit#dd2c4d6d](https://github.com/RVC-Boss/GPT-SoVITS/commit/dd2c4d6d7121bf82d29d0f0e4d788f3b231997c8)
  - İçerik: Eğitimde Çince deney adları desteklendi (önceki sürümlerde hata veriyordu).
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.02.15 [Commit#ccb9b08b](https://github.com/RVC-Boss/GPT-SoVITS/commit/ccb9b08be3c58e102defcc94ff4fd609da9e27ee)~[Commit#895fde46](https://github.com/RVC-Boss/GPT-SoVITS/commit/895fde46e420040ed26aaf0c5b7e99359d9b199b)
  - İçerik: DPO eğitimi zorunlu olmaktan çıkarılıp seçmeli hale getirildi. Seçildiğinde batch boyutu otomatik yarıya indiriliyor. Çıkarım WebUI'sında yeni parametrelerin iletilmemesi sorunu düzeltildi.
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.02.15 [Commit#7b0c3c67](https://github.com/RVC-Boss/GPT-SoVITS/commit/7b0c3c676495c64b2064aa472bff14b5c06206a5)
  - İçerik: Çince ön uç hataları düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.02.16 [PR#499](https://github.com/RVC-Boss/GPT-SoVITS/pull/499)
  - İçerik: Referans metin olmadan giriş yapma desteği eklendi.
  - Tür: Özellik
  - Katkıda Bulunan: Watchtower-Liu
  - İlgili: [Issue#475](https://github.com/RVC-Boss/GPT-SoVITS/issues/475)
- 2024.02.17 [PR#509](https://github.com/RVC-Boss/GPT-SoVITS/pull/509), [PR#507](https://github.com/RVC-Boss/GPT-SoVITS/pull/507), [PR#532](https://github.com/RVC-Boss/GPT-SoVITS/pull/532), [PR#556](https://github.com/RVC-Boss/GPT-SoVITS/pull/556), [PR#559](https://github.com/RVC-Boss/GPT-SoVITS/pull/559)
  - İçerik: Çince ve Japonca ön uç işleme optimizasyonları yapıldı.
  - Tür: Optimizasyon
  - Katkıda Bulunan: KamioRinn, v3cun
- 2024.02.17 [PR#510](https://github.com/RVC-Boss/GPT-SoVITS/pull/511), [PR#511](https://github.com/RVC-Boss/GPT-SoVITS/pull/511)
  - İçerik: Colab genel URL sorunu düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: ChanningWang2018, RVC-Boss
- 2024.02.21 [PR#557](https://github.com/RVC-Boss/GPT-SoVITS/pull/557)
  - İçerik: Mac CPU çıkarımında MPS yerine CPU kullanılarak performans iyileştirildi.
  - Tür: Optimizasyon
  - Katkıda Bulunan: XXXXRT666
- 2024.02.21 [Commit#6da486c1](https://github.com/RVC-Boss/GPT-SoVITS/commit/6da486c15d09e3d99fa42c5e560aaac56b6b4ce1), [Commit#5a171773](https://github.com/RVC-Boss/GPT-SoVITS/commit/5a17177342d2df1e11369f2f4f58d34a3feb1a35)
  - İçerik: Veri işleme sırasında gürültü azaltma seçeneği eklendi (sadece 16kHz örnekleme hızını korur, sadece yüksek arka plan gürültüsü varsa kullanılması önerilir).
  - Tür: Özellik
  - Katkıda Bulunan: RVC-Boss
- 2024.02.28 [PR#573](https://github.com/RVC-Boss/GPT-SoVITS/pull/573)
  - İçerik: Mac'te CPU çıkarımının düzgün çalışması için `is_half` kontrolü düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: XXXXRT666
- 2024.02.28 [PR#610](https://github.com/RVC-Boss/GPT-SoVITS/pull/610)
  - İçerik: UVR5 reverb kaldırma modelinde ayarların ters olması sorunu düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: Yuze Wang

## 202403

- 2024.03.06 [PR#675](https://github.com/RVC-Boss/GPT-SoVITS/pull/675)
  - İçerik: CUDA yokken Faster Whisper için otomatik CPU çıkarımı etkinleştirildi
  - Tür: Optimizasyon
  - Katkıda Bulunan: ShiroDoMain
- 2024.03.06 [Commit#616be20d](https://github.com/RVC-Boss/GPT-SoVITS/commit/616be20db3cf94f1cd663782fea61b2370704193)
  - İçerik: Faster Whisper Çince olmayan ASR kullanırken artık Çince FunASR modelini önceden indirmeye gerek yok
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss
- 2024.03.09 [PR#672](https://github.com/RVC-Boss/GPT-SoVITS/pull/672)
  - İçerik: Çıkarım hızı %50 iyileştirildi (RTX3090 + PyTorch 2.2.1 + CU11.8 + Win10 + Py39 ortamında test edildi)
  - Tür: Optimizasyon
  - Katkıda Bulunan: GoHomeToMacDonal
- 2024.03.10 [PR#721](https://github.com/RVC-Boss/GPT-SoVITS/pull/721)
  - İçerik: Hızlı çıkarım dalı 'fast_inference_' eklendi
  - Tür: Özellik
  - Katkıda Bulunan: ChasonJiang
- 2024.03.13 [PR#761](https://github.com/RVC-Boss/GPT-SoVITS/pull/761)
  - İçerik: CPU ile eğitim desteği eklendi, macOS'ta CPU kullanarak eğitim yapılabilir
  - Tür: Özellik
  - Katkıda Bulunan: Lion-Wu
- 2024.03.19 [PR#804](https://github.com/RVC-Boss/GPT-SoVITS/pull/804), [PR#812](https://github.com/RVC-Boss/GPT-SoVITS/pull/812), [PR#821](https://github.com/RVC-Boss/GPT-SoVITS/pull/821)
  - İçerik: İngilizce metin ön uç iyileştirmeleri
  - Tür: Optimizasyon
  - Katkıda Bulunan: KamioRinn
- 2024.03.30 [PR#894](https://github.com/RVC-Boss/GPT-SoVITS/pull/894)
  - İçerik: API formatı geliştirildi
  - Tür: Optimizasyon
  - Katkıda Bulunan: KamioRinn

## 202404

- 2024.04.03 [PR#917](https://github.com/RVC-Boss/GPT-SoVITS/pull/917)
  - İçerik: UVR5 WebUI'da FFmpeg komut dizgisi biçimlendirmesi düzeltildi
  - Tür: Düzeltme
  - Katkıda Bulunan: StaryLan

## 202405

- 2024.05.02 [PR#953](https://github.com/RVC-Boss/GPT-SoVITS/pull/953)
  - İçerik: SoVITS eğitiminde VQ'nun dondurulmamasından kaynaklanan kalite düşüşü sorunu çözüldü
  - Tür: Düzeltme
  - Katkıda Bulunan: hcwu1993
  - İlgili: [Issue#747](https://github.com/RVC-Boss/GPT-SoVITS/issues/747)
- 2024.05.19 [PR#1102](https://github.com/RVC-Boss/GPT-SoVITS/pull/1102)
  - İçerik: Eğitim verisi işleme sırasında desteklenmeyen diller için hata mesajı eklendi
  - Tür: Optimizasyon
  - Katkıda Bulunan: StaryLan
- 2024.05.27 [PR#1132](https://github.com/RVC-Boss/GPT-SoVITS/pull/1132)
  - İçerik: Hubert çıkarım hatası düzeltildi
  - Tür: Düzeltme
  - Katkıda Bulunan: XXXXRT666

## 202406

- 2024.06.06 [Commit#99f09c8b](https://github.com/RVC-Boss/GPT-SoVITS/commit/99f09c8bdc155c1f4272b511940717705509582a)
  - İçerik: WebUI'da GPT ince ayarında Çince metinlerin BERT özelliklerinin okunmaması nedeniyle çıkarım tutarsızlığı ve kalite düşüşü sorunu düzeltildi
  **Uyarı: Daha önce büyük miktarda veriyle ince ayar yaptıysanız, kaliteyi artırmak için modeli yeniden ayarlamanız önerilir**
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.06.07 [PR#1159](https://github.com/RVC-Boss/GPT-SoVITS/pull/1159)
  - İçerik: `s2_train.py` dosyasında SoVITS eğitim ilerleme çubuğu mantığı düzeltildi
  - Tür: Düzeltme
  - Katkıda Bulunan: pengzhendong
- 2024.06.10 [Commit#501a74ae](https://github.com/RVC-Boss/GPT-SoVITS/commit/501a74ae96789a26b48932babed5eb4e9483a232)
  - İçerik: UVR5 MDXNet'in FFmpeg çağrılarında boşluk içeren yollarla uyumlu olması için dize biçimlendirme düzeltildi
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.06.10 [PR#1168](https://github.com/RVC-Boss/GPT-SoVITS/pull/1168), [PR#1169](https://github.com/RVC-Boss/GPT-SoVITS/pull/1169)
  - İçerik: Saf noktalama işaretleri ve çoklu noktalama işaretli metin girişi işleme mantığı iyileştirildi
  - Tür: Düzeltme
  - Katkıda Bulunan: XXXXRT666
  - İlgili: [Issue#1165](https://github.com/RVC-Boss/GPT-SoVITS/issues/1165)
- 2024.06.13 [Commit#db506705](https://github.com/RVC-Boss/GPT-SoVITS/commit/db50670598f0236613eefa6f2d5a23a271d82041)
  - İçerik: CPU çıkarımında varsayılan batch boyutu ondalık sorunu düzeltildi
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.06.28 [PR#1258](https://github.com/RVC-Boss/GPT-SoVITS/pull/1258), [PR#1265](https://github.com/RVC-Boss/GPT-SoVITS/pull/1265), [PR#1267](https://github.com/RVC-Boss/GPT-SoVITS/pull/1267)
  - İçerik: Gürültü azaltma veya ASR işlemi sırasında istisna oluştuğunda bekleyen tüm ses dosyalarının kapanması sorunu düzeltildi
  - Tür: Düzeltme
  - Katkıda Bulunan: XXXXRT666
- 2024.06.29 [Commit#a208698e](https://github.com/RVC-Boss/GPT-SoVITS/commit/a208698e775155efc95b187b746d153d0f2847ca)
  - İçerik: Çoklu GPU eğitiminde çoklu işlem kayıt mantığı düzeltildi
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2024.06.29 [PR#1251](https://github.com/RVC-Boss/GPT-SoVITS/pull/1251)
  - İçerik: Yinelenen `my_utils.py` dosyası kaldırıldı
  - Tür: Optimizasyon
  - Katkıda Bulunan: aoguai
  - İlgili: [Issue#1189](https://github.com/RVC-Boss/GPT-SoVITS/issues/1189)

## 202407

- 2024.07.06 [PR#1253](https://github.com/RVC-Boss/GPT-SoVITS/pull/1253)
  - İçerik: Noktalama işaretlerine göre bölme işlemi sırasında ondalık sayıların bölünmesi sorunu düzeltildi
  - Tür: Düzeltme
  - Katkıda Bulunan: aoguai
- 2024.07.06 [Commit#b0786f29](https://github.com/RVC-Boss/GPT-SoVITS/commit/b0786f2998f1b2fce6678434524b4e0e8cc716f5)
  - İçerik: Hızlandırılmış çıkarım kodu doğrulandı ve ana dal ile birleştirildi. Temel sürümle aynı çıkarım etkisi garanti edilirken referans metni olmayan modda da hızlandırılmış çıkarım destekleniyor
  - Tür: Optimizasyon
  - Katkıda Bulunan: RVC-Boss, GoHomeToMacDonal
  - İlgili: [PR#672](https://github.com/RVC-Boss/GPT-SoVITS/pull/672)
- 2024.07.13 [PR#1294](https://github.com/RVC-Boss/GPT-SoVITS/pull/1294), [PR#1298](https://github.com/RVC-Boss/GPT-SoVITS/pull/1298)
  - İçerik: i18n taraması yeniden düzenlendi ve çok dilli yapılandırma dosyaları güncellendi
  - Tür: Dokümantasyon
  - Katkıda Bulunan: StaryLan
- 2024.07.13 [PR#1299](https://github.com/RVC-Boss/GPT-SoVITS/pull/1299)
  - İçerik: Kullanıcı dosya yollarındaki son eğik çizgilerin neden olduğu komut satırı hataları düzeltildi
  - Tür: Düzeltme
  - Katkıda Bulunan: XXXXRT666
- 2024.07.19 [PR#756](https://github.com/RVC-Boss/GPT-SoVITS/pull/756)
  - İçerik: GPT eğitiminde özel `bucket_sampler` kullanılırken eğitim adımlarında tutarsızlık sorunu düzeltildi
  - Tür: Düzeltme
  - Katkıda Bulunan: huangxu1991
- 2024.07.23 [Commit#9588a3c5](https://github.com/RVC-Boss/GPT-SoVITS/commit/9588a3c52d9ebdb20b3c5d74f647d12e7c1171c2), [PR#1340](https://github.com/RVC-Boss/GPT-SoVITS/pull/1340)
  - İçerik: Sentez sırasında konuşma hızı ayarlama özelliği eklendi (rastgeleliği sabitleme ve sadece hızı kontrol etme seçeneği dahil). Bu özellik `api.py` dosyasına eklendi
  - Tür: Özellik
  - Katkıda Bulunan: RVC-Boss, 红血球AE3803
- 2024.07.27 [PR#1306](https://github.com/RVC-Boss/GPT-SoVITS/pull/1306), [PR#1356](https://github.com/RVC-Boss/GPT-SoVITS/pull/1356)
  - İçerik: BS-RoFormer vokal eşlik ayırma modeli desteği eklendi.
  - Tür: Yeni Özellik
  - Katkıda Bulunan: KamioRinn
- 2024.07.27 [PR#1351](https://github.com/RVC-Boss/GPT-SoVITS/pull/1351)
  - İçerik: Çince metin ön işleme iyileştirildi.
  - Tür: Yeni Özellik
  - Katkıda Bulunan: KamioRinn

## 202408 (V2 Sürümü)

- 2024.08.01 [PR#1355](https://github.com/RVC-Boss/GPT-SoVITS/pull/1355)
  - İçerik: WebUI'de dosya işlerken yolların otomatik doldurulması.
  - Tür: Chore
  - Katkıda Bulunan: XXXXRT666
- 2024.08.01 [Commit#e62e9653](https://github.com/RVC-Boss/GPT-SoVITS/commit/e62e965323a60a76a025bcaa45268c1ddcbcf05c)
  - İçerik: BS-Roformer için FP16 çıkarım desteği etkinleştirildi.
  - Tür: Performans Optimizasyonu
  - Katkıda Bulunan: RVC-Boss
- 2024.08.01 [Commit#bce451a2](https://github.com/RVC-Boss/GPT-SoVITS/commit/bce451a2d1641e581e200297d01f219aeaaf7299), [Commit#4c8b7612](https://github.com/RVC-Boss/GPT-SoVITS/commit/4c8b7612206536b8b4435997acb69b25d93acb78)
  - İçerik: GPU tanıma mantığı optimize edildi, kullanıcıların girdiği rastgele GPU indekslerini işlemek için kullanıcı dostu mantık eklendi.
  - Tür: Chore
  - Katkıda Bulunan: RVC-Boss
- 2024.08.02 [Commit#ff6c193f](https://github.com/RVC-Boss/GPT-SoVITS/commit/ff6c193f6fb99d44eea3648d82ebcee895860a22)~[Commit#de7ee7c7](https://github.com/RVC-Boss/GPT-SoVITS/commit/de7ee7c7c15a2ec137feb0693b4ff3db61fad758)
  - İçerik: **GPT-SoVITS V2 modeli eklendi.**
  - Tür: Yeni Özellik
  - Katkıda Bulunan: RVC-Boss
- 2024.08.03 [Commit#8a101474](https://github.com/RVC-Boss/GPT-SoVITS/commit/8a101474b5a4f913b4c94fca2e3ca87d0771bae3)
  - İçerik: FunASR kullanarak Kantonca ASR desteği eklendi.
  - Tür: Yeni Özellik
  - Katkıda Bulunan: RVC-Boss
- 2024.08.03 [PR#1387](https://github.com/RVC-Boss/GPT-SoVITS/pull/1387), [PR#1388](https://github.com/RVC-Boss/GPT-SoVITS/pull/1388)
  - İçerik: UI ve zamanlama mantığı optimize edildi.
  - Tür: Chore
  - Katkıda Bulunan: XXXXRT666
- 2024.08.06 [PR#1404](https://github.com/RVC-Boss/GPT-SoVITS/pull/1404), [PR#987](https://github.com/RVC-Boss/GPT-SoVITS/pull/987), [PR#488](https://github.com/RVC-Boss/GPT-SoVITS/pull/488)
  - İçerik: Çok sesli karakter işleme mantığı optimize edildi (Yalnızca V2).
  - Tür: Düzeltme, Yeni Özellik
  - Katkıda Bulunan: KamioRinn, RVC-Boss
- 2024.08.13 [PR#1422](https://github.com/RVC-Boss/GPT-SoVITS/pull/1422)
  - İçerik: Yalnızca bir referans ses yüklenebilme hatası düzeltildi; eksik dosyalar için uyarı açılır pencereleriyle veri seti doğrulama eklendi.
  - Tür: Düzeltme, Chore
  - Katkıda Bulunan: XXXXRT666
- 2024.08.20 [Issue#1508](https://github.com/RVC-Boss/GPT-SoVITS/issues/1508)
  - İçerik: Yukarı akış LangSegment kütüphanesi artık SSML etiketleri kullanarak sayıları, telefon numaralarını, tarihleri ve saatleri optimize ediyor.
  - Tür: Yeni Özellik
  - Katkıda Bulunan: juntaosun
- 2024.08.20 [PR#1503](https://github.com/RVC-Boss/GPT-SoVITS/pull/1503)
  - İçerik: API düzeltildi ve optimize edildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: KamioRinn
- 2024.08.20 [PR#1490](https://github.com/RVC-Boss/GPT-SoVITS/pull/1490)
  - İçerik: `fast_inference` dalı ana dala birleştirildi.
  - Tür: Yeniden Yapılandırma
  - Katkıda Bulunan: ChasonJiang
- 2024.08.21 **GPT-SoVITS V2 sürümü resmi olarak yayınlandı.**

## 202502 (V3 Sürümü)

- 2025.02.11 [Commit#ed207c4b](https://github.com/RVC-Boss/GPT-SoVITS/commit/ed207c4b879d5296e9be3ae5f7b876729a2c43b8)~[Commit#6e2b4918](https://github.com/RVC-Boss/GPT-SoVITS/commit/6e2b49186c5b961f0de41ea485d398dffa9787b4)
  - İçerik: **İnce ayar için 14GB VRAM gerektiren GPT-SoVITS V3 modeli eklendi.**
  - Tür: Yeni Özellik ([Wiki](https://github.com/RVC-Boss/GPT-SoVITS/wiki/GPT%E2%80%90SoVITS%E2%80%90v3%E2%80%90features-(%E6%96%B0%E7%89%B9%E6%80%A7)) referans)
  - Katkıda Bulunan: RVC-Boss
- 2025.02.12 [PR#2032](https://github.com/RVC-Boss/GPT-SoVITS/pull/2032)
  - İçerik: Çok dilli proje dokümantasyonu güncellendi.
  - Tür: Dokümantasyon
  - Katkıda Bulunan: StaryLan
- 2025.02.12 [PR#2033](https://github.com/RVC-Boss/GPT-SoVITS/pull/2033)
  - İçerik: Japonca dokümantasyon güncellendi.
  - Tür: Dokümantasyon
  - Katkıda Bulunan: Fyphen
- 2025.02.12 [PR#2010](https://github.com/RVC-Boss/GPT-SoVITS/pull/2010)
  - İçerik: Dikkat hesaplama mantığı optimize edildi.
  - Tür: Performans Optimizasyonu
  - Katkıda Bulunan: wzy3650
- 2025.02.12 [PR#2040](https://github.com/RVC-Boss/GPT-SoVITS/pull/2040)
  - İçerik: İnce ayar için gradyan kontrol noktası desteği eklendi (12GB VRAM gerektirir).
  - Tür: Yeni Özellik
  - Katkıda Bulunan: Kakaru Hayate
- 2025.02.14 [PR#2047](https://github.com/RVC-Boss/GPT-SoVITS/pull/2047), [PR#2062](https://github.com/RVC-Boss/GPT-SoVITS/pull/2062), [PR#2073](https://github.com/RVC-Boss/GPT-SoVITS/pull/2073)
  - İçerik: Yeni dil bölümleme aracına geçildi, çok dilli karışık metin bölme stratejisi iyileştirildi, sayı ve İngilizce işleme mantığı optimize edildi.
  - Tür: Yeni Özellik
  - Katkıda Bulunan: KamioRinn
- 2025.02.23 [Commit#56509a17](https://github.com/RVC-Boss/GPT-SoVITS/commit/56509a17c918c8d149c48413a672b8ddf437495b)~[Commit#514fb692](https://github.com/RVC-Boss/GPT-SoVITS/commit/514fb692db056a06ed012bc3a5bca2a5b455703e)
  - İçerik: **GPT-SoVITS V3 modeli artık LoRA eğitimini destekliyor (ince ayar için 8GB GPU Belleği gerektirir).**
  - Tür: Yeni Özellik
  - Katkıda Bulunan: RVC-Boss
- 2025.02.23 [PR#2078](https://github.com/RVC-Boss/GPT-SoVITS/pull/2078)
  - İçerik: Vokal ve enstrüman ayırma için Mel Band Roformer model desteği eklendi.
  - Tür: Yeni Özellik
  - Katkıda Bulunan: Sucial
- 2025.02.26 [PR#2112](https://github.com/RVC-Boss/GPT-SoVITS/pull/2112), [PR#2114](https://github.com/RVC-Boss/GPT-SoVITS/pull/2114)
  - İçerik: Çince yollarda MeCab hatası düzeltildi (özel olarak Japonca/Korece veya çok dilli metin bölme için).
  - Tür: Düzeltme
  - Katkıda Bulunan: KamioRinn
- 2025.02.27 [Commit#92961c3f](https://github.com/RVC-Boss/GPT-SoVITS/commit/92961c3f68b96009ff2cd00ce614a11b6c4d026f)~[Commit#250b1c73](https://github.com/RVC-Boss/GPT-SoVITS/commit/250b1c73cba60db18148b21ec5fbce01fd9d19bc)
  - İçerik: V3 modeliyle 24K ses üretirken "boğuk" ses sorununu hafifletmek için **24kHz'den 48kHz'e ses süper çözünürlük modelleri eklendi**.
  - Tür: Yeni Özellik
  - Katkıda Bulunan: RVC-Boss
  - İlgili: [Issue#2085](https://github.com/RVC-Boss/GPT-SoVITS/issues/2085), [Issue#2117](https://github.com/RVC-Boss/GPT-SoVITS/issues/2117)
- 2025.02.28 [PR#2123](https://github.com/RVC-Boss/GPT-SoVITS/pull/2123)
  - İçerik: Çok dilli proje dokümantasyonu güncellendi.
  - Tür: Dokümantasyon
  - Katkıda Bulunan: StaryLan
- 2025.02.28 [PR#2122](https://github.com/RVC-Boss/GPT-SoVITS/pull/2122)
  - İçerik: Model tanımlayamadığında kısa CJK karakterleri için kural tabanlı tespit uygulandı.
  - Tür: Düzeltme
  - Katkıda Bulunan: KamioRinn
  - İlgili: [Issue#2116](https://github.com/RVC-Boss/GPT-SoVITS/issues/2116)
- 2025.02.28 [Commit#c38b1690](https://github.com/RVC-Boss/GPT-SoVITS/commit/c38b16901978c1db79491e16905ea3a37a7cf686), [Commit#a32a2b89](https://github.com/RVC-Boss/GPT-SoVITS/commit/a32a2b893436fad56cc82409121c7fa36a1815d5)
  - İçerik: Sentez hızını kontrol etmek için konuşma hızı parametresi eklendi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2025.02.28 **GPT-SoVITS V3 resmi olarak yayınlandı**.

## 202503

- 2025.03.31 [PR#2236](https://github.com/RVC-Boss/GPT-SoVITS/pull/2236)
  - İçerik: Bağımlılıkların yanlış sürümlerinden kaynaklanan sorunlar düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: XXXXRT666
  - İlgili:
    - PyOpenJTalk: [Issue#1131](https://github.com/RVC-Boss/GPT-SoVITS/issues/1131), [Issue#2231](https://github.com/RVC-Boss/GPT-SoVITS/issues/2231), [Issue#2233](https://github.com/RVC-Boss/GPT-SoVITS/issues/2233).
    - ONNX: [Issue#492](https://github.com/RVC-Boss/GPT-SoVITS/issues/492), [Issue#671](https://github.com/RVC-Boss/GPT-SoVITS/issues/671), [Issue#1192](https://github.com/RVC-Boss/GPT-SoVITS/issues/1192), [Issue#1819](https://github.com/RVC-Boss/GPT-SoVITS/issues/1819), [Issue#1841](https://github.com/RVC-Boss/GPT-SoVITS/issues/1841).
    - Pydantic: [Issue#2230](https://github.com/RVC-Boss/GPT-SoVITS/issues/2230), [Issue#2239](https://github.com/RVC-Boss/GPT-SoVITS/issues/2239).
    - PyTorch-Lightning: [Issue#2174](https://github.com/RVC-Boss/GPT-SoVITS/issues/2174).
- 2025.03.31 [PR#2241](https://github.com/RVC-Boss/GPT-SoVITS/pull/2241)
  - İçerik: **SoVITS v3 için paralel çıkarım etkinleştirildi.**
  - Tür: Yeni Özellik
  - Katkıda Bulunan: ChasonJiang

- Diğer küçük hatalar düzeltildi.

- ONNX çalışma zamanı GPU çıkarım desteği için entegre paket düzeltmeleri:
  - Tür: Düzeltme
  - Detaylar:
    - G2PW içindeki ONNX modelleri CPU'dan GPU çıkarımına geçirildi, CPU darboğazı önemli ölçüde azaltıldı;
    - foxjoy yankı giderme modeli artık GPU çıkarımını destekliyor.

## 202504 (V4 Sürümü)

- 2025.04.01 [Commit#6a60e5ed](https://github.com/RVC-Boss/GPT-SoVITS/commit/6a60e5edb1817af4a61c7a5b196c0d0f1407668f)
  - İçerik: SoVITS v3 paralel çıkarımı kilit açıldı; asenkron model yükleme mantığı düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2025.04.07 [PR#2255](https://github.com/RVC-Boss/GPT-SoVITS/pull/2255)
  - İçerik: Ruff ile kod biçimlendirme; G2PW bağlantısı güncellendi.
  - Tür: Stil
  - Katkıda Bulunan: XXXXRT666
- 2025.04.15 [PR#2290](https://github.com/RVC-Boss/GPT-SoVITS/pull/2290)
  - İçerik: Dokümantasyon temizlendi; Python 3.11 desteği eklendi; yükleyiciler güncellendi.
  - Tür: Chore
  - Katkıda Bulunan: XXXXRT666
- 2025.04.20 [PR#2300](https://github.com/RVC-Boss/GPT-SoVITS/pull/2300)
  - İçerik: Colab, kurulum dosyaları ve model indirmeleri güncellendi.
  - Tür: Chore
  - Katkıda Bulunan: XXXXRT666
- 2025.04.20 [Commit#e0c452f0](https://github.com/RVC-Boss/GPT-SoVITS/commit/e0c452f0078e8f7eb560b79a54d75573fefa8355)~[Commit#9d481da6](https://github.com/RVC-Boss/GPT-SoVITS/commit/9d481da610aa4b0ef8abf5651fd62800d2b4e8bf)
  - İçerik: **GPT-SoVITS V4 modeli eklendi.**
  - Tür: Yeni Özellik
  - Katkıda Bulunan: RVC-Boss
- 2025.04.21 [Commit#8b394a15](https://github.com/RVC-Boss/GPT-SoVITS/commit/8b394a15bce8e1d85c0b11172442dbe7a6017ca2)~[Commit#bc2fe5ec](https://github.com/RVC-Boss/GPT-SoVITS/commit/bc2fe5ec86536c77bb3794b4be263ac87e4fdae6), [PR#2307](https://github.com/RVC-Boss/GPT-SoVITS/pull/2307)
  - İçerik: V4 için paralel çıkarım etkinleştirildi.
  - Tür: Yeni Özellik
  - Katkıda Bulunan: RVC-Boss, ChasonJiang
- 2025.04.22 [Commit#7405427a](https://github.com/RVC-Boss/GPT-SoVITS/commit/7405427a0ab2a43af63205df401fd6607a408d87)~[Commit#590c83d7](https://github.com/RVC-Boss/GPT-SoVITS/commit/590c83d7667c8d4908f5bdaf2f4c1ba8959d29ff), [PR#2309](https://github.com/RVC-Boss/GPT-SoVITS/pull/2309)
  - İçerik: Model sürümü parametre aktarımı düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss, ChasonJiang
- 2025.04.22 [Commit#fbdab94e](https://github.com/RVC-Boss/GPT-SoVITS/commit/fbdab94e17d605d85841af6f94f40a45976dd1d9), [PR#2310](https://github.com/RVC-Boss/GPT-SoVITS/pull/2310)
  - İçerik: Numpy ve Numba sürüm uyumsuzluğu sorunu düzeltildi; librosa sürümü güncellendi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss, XXXXRT666
  - İlgili: [Issue#2308](https://github.com/RVC-Boss/GPT-SoVITS/issues/2308)
- **2025.04.22 GPT-SoVITS V4 resmi olarak yayınlandı**.
- 2025.04.22 [PR#2311](https://github.com/RVC-Boss/GPT-SoVITS/pull/2311)
  - İçerik: Gradio parametreleri güncellendi.
  - Tür: Chore
  - Katkıda Bulunan: XXXXRT666
- 2025.04.25 [PR#2322](https://github.com/RVC-Boss/GPT-SoVITS/pull/2322)
  - İçerik: Colab/Kaggle notebook betikleri iyileştirildi.
  - Tür: Chore
  - Katkıda Bulunan: XXXXRT666

## 202505

- 2025.05.26 [PR#2351](https://github.com/RVC-Boss/GPT-SoVITS/pull/2351)
  - İçerik: Docker ve Windows otomatik derleme betikleri iyileştirildi; ön işleme biçimlendirme eklendi.
  - Tür: Chore
  - Katkıda Bulunan: XXXXRT666
- 2025.05.26 [PR#2408](https://github.com/RVC-Boss/GPT-SoVITS/pull/2408)
  - İçerik: Çok dilli metin bölme ve tanıma mantığı optimize edildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: KamioRinn
  - İlgili: [Issue#2404](https://github.com/RVC-Boss/GPT-SoVITS/issues/2404)
- 2025.05.26 [PR#2377](https://github.com/RVC-Boss/GPT-SoVITS/pull/2377)
  - İçerik: SoVITS V3/V4 çıkarım hızını %10 artırmak için önbellekleme stratejileri uygulandı.
  - Tür: Performans Optimizasyonu
  - Katkıda Bulunan: Kakaru Hayate
- 2025.05.26 [Commit#4d9d56b1](https://github.com/RVC-Boss/GPT-SoVITS/commit/4d9d56b19638dc434d6eefd9545e4d8639a3e072), [Commit#8c705784](https://github.com/RVC-Boss/GPT-SoVITS/commit/8c705784c50bf438c7b6d0be33a9e5e3cb90e6b2), [Commit#fafe4e7f](https://github.com/RVC-Boss/GPT-SoVITS/commit/fafe4e7f120fba56c5f053c6db30aa675d5951ba)
  - İçerik: Açıklama arayüzü uyarı ile güncellendi: her sayfa tamamlandıktan sonra "Metni Gönder"e tıklayın, aksi takdirde değişiklikler kaydedilmez.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss
- 2025.05.29 [Commit#1934fc1e](https://github.com/RVC-Boss/GPT-SoVITS/commit/1934fc1e1b22c4c162bba1bbe7d7ebb132944cdc)
  - İçerik: UVR5 ve ONNX yankı giderme modellerinde, FFmpeg'in orijinal yollarında boşluk bulunan MP3/M4A dosyalarını kodlarken oluşan hatalar düzeltildi.
  - Tür: Düzeltme
  - Katkıda Bulunan: RVC-Boss

## 202506 (V2Pro Serisi)

- 2025.06.03 [PR#2420](https://github.com/RVC-Boss/GPT-SoVITS/pull/2420)
  - İçerik: Çok dilli proje dokümantasyonu güncellendi
  - Tür: Dokümantasyon
  - Katkıda Bulunan: StaryLan
- 2025.06.04 [PR#2417](https://github.com/RVC-Boss/GPT-SoVITS/pull/2417)
  - İçerik: TorchScript ile V4 dışa aktarma desteği eklendi
  - Tür: Özellik
  - Katkıda Bulunan: L-jasmine
- 2025.06.04 [Commit#b7c0c5ca](https://github.com/RVC-Boss/GPT-SoVITS/commit/b7c0c5ca878bcdd419fd86bf80dba431a6653356)~[Commit#298ebb03](https://github.com/RVC-Boss/GPT-SoVITS/commit/298ebb03c5a719388527ae6a586c7ea960344e70)
  - İçerik: **GPT-SoVITS V2Pro Serisi model eklendi (V2Pro, V2ProPlus)**
  - Tür: Özellik
  - Katkıda Bulunan: RVC-Boss
- 2025.06.05 [PR#2426](https://github.com/RVC-Boss/GPT-SoVITS/pull/2426)
  - İçerik: `config/inference_webui` başlatma hatası düzeltildi
  - Tür: Hata Düzeltme
  - Katkıda Bulunan: StaryLan
- 2025.06.05 [PR#2427](https://github.com/RVC-Boss/GPT-SoVITS/pull/2427), [Commit#7d70852a](https://github.com/RVC-Boss/GPT-SoVITS/commit/7d70852a3f67c3b52e3a62857f8663d529efc8cd), [PR#2434](https://github.com/RVC-Boss/GPT-SoVITS/pull/2434)
  - İçerik: Otomatik hassasiyet algılama mantığı optimize edildi; WebUI önyüz modüllerine katlanabilir özellik eklendi
  - Tür: Yeni Özellik
  - Katkıda Bulunanlar: XXXXRT666, RVC-Boss
