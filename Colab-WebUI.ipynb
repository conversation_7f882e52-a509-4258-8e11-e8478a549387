{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "view-in-github"}, "source": ["<a href=\"https://colab.research.google.com/github/RVC-Boss/GPT-SoVITS/blob/main/Colab-WebUI.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# GPT-SoVITS WebUI"]}, {"cell_type": "markdown", "metadata": {"id": "_o6a8GS2lWQM"}, "source": ["## Env Setup (Run Once Only)\n", "## 环境配置, 只需运行一次"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile /content/setup.sh\n", "set -e\n", "\n", "cd /content\n", "\n", "git clone https://github.com/RVC-Boss/GPT-SoVITS.git\n", "\n", "cd GPT-SoVITS\n", "\n", "if conda env list | awk '{print $1}' | grep -Fxq \"GPTSoVITS\"; then\n", "    :\n", "else\n", "    conda create -n GPTSoVITS python=3.10 -y\n", "fi\n", "\n", "source activate GPTSoVITS\n", "\n", "pip install ipykernel\n", "\n", "bash install.sh --device CU126 --source HF --download-uvr5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -q condacolab\n", "import condacolab\n", "condacolab.install_from_url(\"https://repo.anaconda.com/archive/Anaconda3-2024.10-1-Linux-x86_64.sh\")\n", "!cd /content && bash setup.sh"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Launch WebUI\n", "## 启动 WebUI"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4oRGUzkrk8C7"}, "outputs": [], "source": ["!cd /content/GPT-SoVITS && source activate GPTSoVITS && export is_share=True && python webui.py"]}], "metadata": {"accelerator": "GPU", "colab": {"include_colab_link": true, "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}