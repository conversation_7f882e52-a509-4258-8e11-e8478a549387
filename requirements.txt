--no-binary=opencc
numpy<2.0
scipy
tensorboard
librosa==0.10.2
numba
pytorch-lightning>=2.4
gradio<5
ffmpeg-python
onnxruntime; platform_machine == "aarch64" or platform_machine == "arm64"
onnxruntime-gpu; platform_machine == "x86_64" or platform_machine == "AMD64"
tqdm
funasr==1.0.27
cn2an
pypinyin
pyopenjtalk>=0.4.1
g2p_en
torchaudio
modelscope==1.10.0
sentencepiece
transformers>=4.43,<=4.50
peft
chardet
PyYAML
psutil
jieba_fast
jieba
split-lang
fast_langdetect>=0.3.1
wordsegment
rotary_embedding_torch
ToJyutping 
g2pk2
ko_pron
opencc
python_mecab_ko; sys_platform != 'win32'
fastapi[standard]>=0.115.2
x_transformers
torchmetrics<=1.5
pydantic<=2.10.6
ctranslate2>=4.0,<5
huggingface_hub>=0.13
tokenizers>=0.13,<1
av>=11
tqdm
