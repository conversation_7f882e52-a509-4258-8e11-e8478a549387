{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/RVC-Boss/GPT-SoVITS/blob/main/Colab-Inference.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# GPT-SoVITS Infer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Env Setup (Run Once Only)\n", "## 环境配置, 只需运行一次"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "e9b7iFV3dm1f"}, "outputs": [], "source": ["%%writefile /content/setup.sh\n", "set -e\n", "\n", "cd /content\n", "\n", "git clone https://github.com/RVC-Boss/GPT-SoVITS.git\n", "\n", "cd GPT-SoVITS\n", "\n", "mkdir -p GPT_weights\n", "\n", "mkdir -p SoVITS_weights\n", "\n", "if conda env list | awk '{print $1}' | grep -Fxq \"GPTSoVITS\"; then\n", "    :\n", "else\n", "    conda create -n GPTSoVITS python=3.10 -y\n", "fi\n", "\n", "source activate GPTSoVITS\n", "\n", "pip install ipykernel\n", "\n", "bash install.sh --device CU126 --source HF"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2."]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "0NgxXg5sjv7z"}, "outputs": [], "source": ["%pip install -q condacolab\n", "import condacolab\n", "condacolab.install_from_url(\"https://repo.anaconda.com/archive/Anaconda3-2024.10-1-Linux-x86_64.sh\")\n", "!cd /content && bash setup.sh"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Download Model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Download From HuggingFace"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "vbZY-LnM0tzq"}, "outputs": [], "source": ["# Modify These\n", "USER_ID = \"AkitoP\"\n", "REPO_NAME = \"GPT-SoVITS-v2-aegi\"\n", "BRANCH = \"main\"\n", "GPT_PATH = \"new_aegigoe-e100.ckpt\"\n", "SOVITS_PATH = \"new_aegigoe_e60_s32220.pth\"\n", "\n", "# Do Not Modify\n", "HF_BASE = \"https://huggingface.co\"\n", "REPO_ID = f\"{USER_ID}/{REPO_NAME}\"\n", "GPT_URL = f\"{HF_BASE}/{REPO_ID}/blob/{BRANCH}/{GPT_PATH}\"\n", "SOVITS_URL = f\"{HF_BASE}/{REPO_ID}/blob/{BRANCH}/{SOVITS_PATH}\"\n", "\n", "!cd \"/content/GPT-SoVITS/GPT_weights\" && wget \"{GPT_URL}\"\n", "!cd \"/content/GPT-SoVITS/SoVITS_weights\" && wget \"{SOVITS_URL}\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Download From ModelScope"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Modify These\n", "USER_ID = \"aihobbyist\"\n", "REPO_NAME = \"GPT-SoVits-V2-models\"\n", "BRANCH = \"master\"\n", "GPT_PATH = \"Genshin_Impact/EN/GPT_GenshinImpact_EN_5.1.ckpt\"\n", "SOVITS_PATH = \"Wuthering_Waves/CN/SV_WutheringWaves_CN_1.3.pth\"\n", "\n", "# Do Not Modify\n", "HF_BASE = \"https://www.modelscope.cn/models\"\n", "REPO_ID = f\"{USER_ID}/{REPO_NAME}\"\n", "GPT_URL = f\"{HF_BASE}/{REPO_ID}/resolve/{BRANCH}/{GPT_PATH}\"\n", "SOVITS_URL = f\"{HF_BASE}/{REPO_ID}/resolve/{BRANCH}/{SOVITS_PATH}\"\n", "\n", "!cd \"/content/GPT-SoVITS/GPT_weights\" && wget \"{GPT_URL}\"\n", "!cd \"/content/GPT-SoVITS/SoVITS_weights\" && wget \"{SOVITS_URL}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Launch WebUI\n", "# 启动 WebUI"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "4oRGUzkrk8C7"}, "outputs": [], "source": ["!cd /content/GPT-SoVITS && source activate GPTSoVITS && export is_share=True && python webui.py"]}], "metadata": {"accelerator": "GPU", "colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}