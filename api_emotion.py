"""
# api_emotion.py 情感标签版API
# 基于api_v2.py修改，支持根据句子的情绪标签自动匹配对应的参考音频

` python api_emotion.py -a 127.0.0.1 -p 9880 -c GPT_SoVITS/configs/tts_infer.yaml `

## 执行参数:
    `-a` - `绑定地址, 默认"127.0.0.1"`
    `-p` - `绑定端口, 默认9880`
    `-c` - `TTS配置文件路径, 默认"GPT_SoVITS/configs/tts_infer.yaml"`
    `-e` - `情感音频映射配置文件路径, 默认"emotion_config.json"`

## 调用:

### 推理

endpoint: `/tts`
GET:
```
http://127.0.0.1:9880/tts?text=[开心]今天能吃红烧肉了！&text_lang=zh&prompt_lang=zh&text_split_method=cut5&batch_size=1&media_type=wav&streaming_mode=true
```

POST:
```json
{
    "text": "[开心]今天能吃红烧肉了！",  # str.(required) text to be synthesized
    "text_lang": "zh",                 # str.(required) language of the text to be synthesized
    "prompt_lang": "zh",               # str.(required) language of the prompt text for the reference audio
    "top_k": 5,                        # int. top k sampling
    "top_p": 1,                        # float. top p sampling
    "temperature": 1,                  # float. temperature for sampling
    "text_split_method": "cut0",       # str. text split method, see text_segmentation_method.py for details.
    "batch_size": 1,                   # int. batch size for inference
    "batch_threshold": 0.75,           # float. threshold for batch splitting.
    "split_bucket": true,              # bool. whether to split the batch into multiple buckets.
    "speed_factor":1.0,                # float. control the speed of the synthesized audio.
    "streaming_mode": false,           # bool. whether to return a streaming response.
    "seed": -1,                        # int. random seed for reproducibility.
    "parallel_infer": true,            # bool. whether to use parallel inference.
    "repetition_penalty": 1.35,        # float. repetition penalty for T2S model.
    "sample_steps": 32,                # int. number of sampling steps for VITS model V3.
    "super_sampling": false            # bool. whether to use super-sampling for audio when using VITS model V3.
}
```

RESP:
成功: 直接返回 wav 音频流， http code 200
失败: 返回包含错误信息的 json, http code 400

### 命令控制

endpoint: `/control`

command:
"restart": 重新运行
"exit": 结束运行

GET:
```
http://127.0.0.1:9880/control?command=restart
```
POST:
```json
{
    "command": "restart"
}
```

RESP: 无


### 切换GPT模型

endpoint: `/set_gpt_weights`

GET:
```
http://127.0.0.1:9880/set_gpt_weights?weights_path=GPT_SoVITS/pretrained_models/s1bert25hz-2kh-longer-epoch=68e-step=50232.ckpt
```
RESP:
成功: 返回"success", http code 200
失败: 返回包含错误信息的 json, http code 400


### 切换Sovits模型

endpoint: `/set_sovits_weights`

GET:
```
http://127.0.0.1:9880/set_sovits_weights?weights_path=GPT_SoVITS/pretrained_models/s2G488k.pth
```

RESP:
成功: 返回"success", http code 200
失败: 返回包含错误信息的 json, http code 400

### 设置情感音频映射

endpoint: `/set_emotion_mapping`

GET:
```
http://127.0.0.1:9880/set_emotion_mapping?emotion=开心&ref_audio_path=happy.wav&prompt_text=我很开心&prompt_lang=zh
```

RESP:
成功: 返回"success", http code 200
失败: 返回包含错误信息的 json, http code 400

### 获取情感音频映射

endpoint: `/get_emotion_mapping`

GET:
```
http://127.0.0.1:9880/get_emotion_mapping
```

RESP:
成功: 返回情感映射配置, http code 200
失败: 返回包含错误信息的 json, http code 400

"""

import os
import sys
import traceback
import json
import re
from typing import Generator, Dict, Any, Optional

now_dir = os.getcwd()
sys.path.append(now_dir)
sys.path.append("%s/GPT_SoVITS" % (now_dir))

import argparse
import subprocess
import wave
import signal
import numpy as np
import soundfile as sf
from fastapi import FastAPI, Response
from fastapi.responses import StreamingResponse, JSONResponse
import uvicorn
from io import BytesIO
from datetime import datetime
import time
import threading
from copy import deepcopy
from tools.i18n.i18n import I18nAuto
from GPT_SoVITS.TTS_infer_pack.TTS import TTS, TTS_Config
from GPT_SoVITS.TTS_infer_pack.text_segmentation_method import get_method_names as get_cut_method_names
from pydantic import BaseModel

i18n = I18nAuto()
cut_method_names = get_cut_method_names()

# 情感标签正则表达式
EMOTION_PATTERN = r'\[(.*?)\](.*)'

class EmotionConfig:
    def __init__(self, config_path: str = "emotion_config.json"):
        self.config_path = config_path
        self.emotion_mapping = {}
        self.load_config()
    
    def load_config(self):
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, "r", encoding="utf-8") as f:
                    self.emotion_mapping = json.load(f)
            except Exception as e:
                print(f"加载情感配置文件失败: {e}")
                self.emotion_mapping = {}
        else:
            self.emotion_mapping = {}
            self.save_config()
    
    def save_config(self):
        try:
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(self.emotion_mapping, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存情感配置文件失败: {e}")
            return False
    
    def set_emotion_mapping(self, emotion: str, ref_audio_path: str, prompt_text: str, prompt_lang: str) -> bool:
        self.emotion_mapping[emotion] = {
            "ref_audio_path": ref_audio_path,
            "prompt_text": prompt_text,
            "prompt_lang": prompt_lang
        }
        return self.save_config()
    
    def get_emotion_mapping(self, emotion: str) -> Optional[Dict[str, str]]:
        return self.emotion_mapping.get(emotion)

parser = argparse.ArgumentParser(description="GPT-SoVITS api with emotion support")
parser.add_argument("-c", "--tts_config", type=str, default="GPT_SoVITS/configs/tts_infer.yaml", help="tts_infer路径")
parser.add_argument("-a", "--bind_addr", type=str, default="127.0.0.1", help="default: 127.0.0.1")
parser.add_argument("-p", "--port", type=int, default="9880", help="default: 9880")
parser.add_argument("-e", "--emotion_config", type=str, default="emotion_config.json", help="情感音频映射配置文件路径")
parser.add_argument("-o", "--output_root", type=str, default="emotion_outputs", help="合成音频保存根目录")
parser.add_argument("--disable_save", action="store_true", default=False, help="关闭服务端自动保存音频")
parser.add_argument("--auto_prewarm", action="store_true", default=False, help="启动后后台预热emotion_config中所有情绪")
args = parser.parse_args()
config_path = args.tts_config
port = args.port
host = args.bind_addr
emotion_config_path = args.emotion_config
argv = sys.argv
output_root = args.output_root
enable_save = not args.disable_save
auto_prewarm = args.auto_prewarm

if config_path in [None, ""]:
    config_path = "GPT-SoVITS/configs/tts_infer.yaml"

tts_config = TTS_Config(config_path)
print(tts_config)
tts_pipeline = TTS(tts_config)
emotion_config = EmotionConfig(emotion_config_path)

APP = FastAPI()

# 情感 -> 预热后的prompt缓存
emotion_prompt_cache = {}
emotion_cache_stats = {}
_prewarm_lock = threading.RLock()

def prepare_emotion_cache(emotion: str, ref_audio_path: str, prompt_text: str, prompt_lang: str):
    if not emotion:
        return
    if emotion in emotion_prompt_cache:
        return
    with _prewarm_lock:
        # 备份现有prompt_cache，避免影响并发请求
        prev_cache = deepcopy(tts_pipeline.prompt_cache)
        # 1) 预热参考音频（生成 refer_spec 等）
        t_ref0 = time.perf_counter()
        tts_pipeline.set_ref_audio(ref_audio_path)
        t_ref1 = time.perf_counter()
        # 2) 预热参考文本（生成 phones/bert_features/norm_text）
        t_txt0 = time.perf_counter()
        phones, bert_features, norm_text = tts_pipeline.text_preprocessor.segment_and_extract_feature_for_text(
            prompt_text.strip("\n"), prompt_lang, tts_config.version
        )
        t_txt1 = time.perf_counter()
        tts_pipeline.prompt_cache["prompt_text"] = prompt_text
        tts_pipeline.prompt_cache["prompt_lang"] = prompt_lang
        tts_pipeline.prompt_cache["phones"] = phones
        tts_pipeline.prompt_cache["bert_features"] = bert_features
        tts_pipeline.prompt_cache["norm_text"] = norm_text
        # 3) 存储深拷贝
        emotion_prompt_cache[emotion] = deepcopy(tts_pipeline.prompt_cache)
        emotion_cache_stats[emotion] = {
            "status": "miss",
            "ref_ms": int((t_ref1 - t_ref0) * 1000),
            "prompt_ms": int((t_txt1 - t_txt0) * 1000),
            "total_ms": int((t_txt1 - t_ref0) * 1000),
        }
        # 恢复先前缓存，避免干扰正在服务的请求
        tts_pipeline.prompt_cache = prev_cache

class TTS_Request(BaseModel):
    text: str = None
    text_lang: str = None
    ref_audio_path: str = None
    aux_ref_audio_paths: list = None
    prompt_lang: str = None
    prompt_text: str = ""
    top_k: int = 5
    top_p: float = 1
    temperature: float = 1
    text_split_method: str = "cut5"
    batch_size: int = 1
    batch_threshold: float = 0.75
    split_bucket: bool = True
    speed_factor: float = 1.0
    fragment_interval: float = 0.3
    seed: int = -1
    media_type: str = "wav"
    streaming_mode: bool = False
    parallel_infer: bool = True
    repetition_penalty: float = 1.35
    sample_steps: int = 32
    super_sampling: bool = False

def extract_emotion(text: str) -> tuple[str, str]:
    """
    从文本中提取情感标签和实际文本
    返回 (情感标签, 实际文本)
    如果没有情感标签，返回 (None, 原文本)
    """
    match = re.match(EMOTION_PATTERN, text)
    if match:
        emotion = match.group(1)
        actual_text = match.group(2)
        return emotion, actual_text
    return None, text

def pack_ogg(io_buffer: BytesIO, data: np.ndarray, rate: int):
    with sf.SoundFile(io_buffer, mode="w", samplerate=rate, channels=1, format="ogg") as audio_file:
        audio_file.write(data)
    return io_buffer

def pack_raw(io_buffer: BytesIO, data: np.ndarray, rate: int):
    io_buffer.write(data.tobytes())
    return io_buffer

def pack_wav(io_buffer: BytesIO, data: np.ndarray, rate: int):
    io_buffer = BytesIO()
    sf.write(io_buffer, data, rate, format="wav")
    return io_buffer

def pack_aac(io_buffer: BytesIO, data: np.ndarray, rate: int):
    process = subprocess.Popen(
        [
            "ffmpeg",
            "-f",
            "s16le",  # 输入16位有符号小端整数PCM
            "-ar",
            str(rate),  # 设置采样率
            "-ac",
            "1",  # 单声道
            "-i",
            "pipe:0",  # 从管道读取输入
            "-c:a",
            "aac",  # 音频编码器为AAC
            "-b:a",
            "192k",  # 比特率
            "-vn",  # 不包含视频
            "-f",
            "adts",  # 输出AAC数据流格式
            "pipe:1",  # 将输出写入管道
        ],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )
    out, _ = process.communicate(input=data.tobytes())
    io_buffer.write(out)
    return io_buffer

def pack_audio(io_buffer: BytesIO, data: np.ndarray, rate: int, media_type: str):
    if media_type == "ogg":
        io_buffer = pack_ogg(io_buffer, data, rate)
    elif media_type == "aac":
        io_buffer = pack_aac(io_buffer, data, rate)
    elif media_type == "wav":
        io_buffer = pack_wav(io_buffer, data, rate)
    else:
        io_buffer = pack_raw(io_buffer, data, rate)
    io_buffer.seek(0)
    return io_buffer

def wave_header_chunk(frame_input=b"", channels=1, sample_width=2, sample_rate=32000):
    wav_buf = BytesIO()
    with wave.open(wav_buf, "wb") as vfout:
        vfout.setnchannels(channels)
        vfout.setsampwidth(sample_width)
        vfout.setframerate(sample_rate)
        vfout.writeframes(frame_input)

    wav_buf.seek(0)
    return wav_buf.read()

def handle_control(command: str):
    if command == "restart":
        os.execl(sys.executable, sys.executable, *argv)
    elif command == "exit":
        os.kill(os.getpid(), signal.SIGTERM)
        exit(0)

def check_params(req: dict):
    text: str = req.get("text", "")
    text_lang: str = req.get("text_lang", "")
    ref_audio_path: str = req.get("ref_audio_path", "")
    streaming_mode: bool = req.get("streaming_mode", False)
    media_type: str = req.get("media_type", "wav")
    prompt_lang: str = req.get("prompt_lang", "")
    prompt_text: str = req.get("prompt_text", "")
    text_split_method: str = req.get("text_split_method", "cut5")

    # 提取情感标签和实际文本
    emotion, actual_text = extract_emotion(text)

    # 如果有情感标签，查找对应的参考音频配置
    if emotion:
        emotion_mapping = emotion_config.get_emotion_mapping(emotion)
        if emotion_mapping:
            # 使用情感映射中的参考音频和提示文本
            ref_audio_path = emotion_mapping.get("ref_audio_path", ref_audio_path)
            if not prompt_text:
                prompt_text = emotion_mapping.get("prompt_text", "")
            if not prompt_lang:
                prompt_lang = emotion_mapping.get("prompt_lang", "")
            # 回写到请求体，确保推理阶段可用
            req["text"] = actual_text
            req["ref_audio_path"] = ref_audio_path
            req["prompt_text"] = prompt_text
            req["prompt_lang"] = prompt_lang
            req["emotion_label"] = emotion
            # 准备并缓存该情感的prompt（仅首次）
            try:
                if emotion not in emotion_prompt_cache:
                    prepare_emotion_cache(emotion, ref_audio_path, prompt_text, prompt_lang)
                    req["emotion_cache_prepared"] = True
                else:
                    # 允许映射更新后强制刷新
                    if (
                        emotion_prompt_cache[emotion].get("ref_audio_path") != ref_audio_path
                        or emotion_prompt_cache[emotion].get("prompt_text") != prompt_text
                        or emotion_prompt_cache[emotion].get("prompt_lang") != prompt_lang
                    ):
                        prepare_emotion_cache(emotion, ref_audio_path, prompt_text, prompt_lang)
                        req["emotion_cache_prepared"] = True
                    else:
                        req["emotion_cache_prepared"] = False
            except Exception as _:
                pass
    
    if text in [None, ""]:
        return JSONResponse(status_code=400, content={"message": "text is required"})
    if text_lang in [None, ""]:
        return JSONResponse(status_code=400, content={"message": "text_lang is required"})
    elif text_lang.lower() not in tts_config.languages:
        return JSONResponse(
            status_code=400,
            content={"message": f"text_lang: {text_lang} is not supported in version {tts_config.version}"},
        )
    if prompt_lang in [None, ""]:
        return JSONResponse(status_code=400, content={"message": "prompt_lang is required"})
    elif prompt_lang.lower() not in tts_config.languages:
        return JSONResponse(
            status_code=400,
            content={"message": f"prompt_lang: {prompt_lang} is not supported in version {tts_config.version}"},
        )
    
    # 如果没有参考音频路径，返回错误
    if ref_audio_path in [None, ""]:
        return JSONResponse(status_code=400, content={"message": "ref_audio_path is required. No matching emotion found."})
    
    if media_type not in ["wav", "raw", "ogg", "aac"]:
        return JSONResponse(status_code=400, content={"message": f"media_type: {media_type} is not supported"})
    elif media_type == "ogg" and not streaming_mode:
        return JSONResponse(status_code=400, content={"message": "ogg format is not supported in non-streaming mode"})

    if text_split_method not in cut_method_names:
        return JSONResponse(
            status_code=400, content={"message": f"text_split_method:{text_split_method} is not supported"}
        )

    return None

async def tts_handle(req: dict):
    """
    Text to speech handler with emotion support.
    """
    streaming_mode = req.get("streaming_mode", False)
    return_fragment = req.get("return_fragment", False)
    media_type = req.get("media_type", "wav")

    check_res = check_params(req)
    if check_res is not None:
        return check_res

    if streaming_mode or return_fragment:
        req["return_fragment"] = True

    try:
        # 如果有缓存的情感prompt，先写入到管线的prompt_cache以跳过重复预处理
        emotion = req.get("emotion_label")
        if emotion and (emotion in emotion_prompt_cache):
            tts_pipeline.prompt_cache = deepcopy(emotion_prompt_cache[emotion])
            # 标记命中
            try:
                emotion_cache_stats[emotion]["status"] = "hit"
            except Exception:
                pass
        tts_generator = tts_pipeline.run(req)

        if streaming_mode:
            def streaming_generator(tts_generator: Generator, media_type: str):
                if_frist_chunk = True
                for sr, chunk in tts_generator:
                    if if_frist_chunk and media_type == "wav":
                        yield wave_header_chunk(sample_rate=sr)
                        media_type = "raw"
                        if_frist_chunk = False
                    yield pack_audio(BytesIO(), chunk, sr, media_type).getvalue()

            return StreamingResponse(
                streaming_generator(
                    tts_generator,
                    media_type,
                ),
                media_type=f"audio/{media_type}",
                headers={
                    "X-Emotion-Cache": "prepared" if req.get("emotion_cache_prepared") else ("hit" if emotion else "none")
                },
            )

        else:
            sr, audio_data = next(tts_generator)
            packed = pack_audio(BytesIO(), audio_data, sr, media_type).getvalue()
            # 服务端保存音频（仅非流式）
            if enable_save:
                try:
                    emotion = req.get("emotion_label") or "default"
                    save_dir = os.path.join(output_root, emotion)
                    os.makedirs(save_dir, exist_ok=True)
                    ts = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                    ext = "wav" if media_type == "wav" else ("ogg" if media_type == "ogg" else ("aac" if media_type == "aac" else "raw"))
                    save_path = os.path.join(save_dir, f"{ts}.{ext}")
                    with open(save_path, "wb") as f:
                        f.write(packed)
                except Exception:
                    pass
            return Response(packed, media_type=f"audio/{media_type}", headers={
                "X-Emotion-Cache": "prepared" if req.get("emotion_cache_prepared") else ("hit" if emotion else "none")
            })
    except Exception as e:
        return JSONResponse(status_code=400, content={"message": "tts failed", "Exception": str(e)})

@APP.get("/control")
async def control(command: str = None):
    if command is None:
        return JSONResponse(status_code=400, content={"message": "command is required"})
    handle_control(command)

@APP.get("/tts")
async def tts_get_endpoint(
    text: str = None,
    text_lang: str = None,
    ref_audio_path: str = None,
    aux_ref_audio_paths: list = None,
    prompt_lang: str = None,
    prompt_text: str = "",
    top_k: int = 5,
    top_p: float = 1,
    temperature: float = 1,
    text_split_method: str = "cut0",
    batch_size: int = 1,
    batch_threshold: float = 0.75,
    split_bucket: bool = True,
    speed_factor: float = 1.0,
    fragment_interval: float = 0.3,
    seed: int = -1,
    media_type: str = "wav",
    streaming_mode: bool = False,
    parallel_infer: bool = True,
    repetition_penalty: float = 1.35,
    sample_steps: int = 32,
    super_sampling: bool = False,
):
    req = {
        "text": text,
        "text_lang": text_lang.lower() if text_lang else None,
        "ref_audio_path": ref_audio_path,
        "aux_ref_audio_paths": aux_ref_audio_paths,
        "prompt_text": prompt_text,
        "prompt_lang": prompt_lang.lower() if prompt_lang else None,
        "top_k": top_k,
        "top_p": top_p,
        "temperature": temperature,
        "text_split_method": text_split_method,
        "batch_size": int(batch_size),
        "batch_threshold": float(batch_threshold),
        "speed_factor": float(speed_factor),
        "split_bucket": split_bucket,
        "fragment_interval": fragment_interval,
        "seed": seed,
        "media_type": media_type,
        "streaming_mode": streaming_mode,
        "parallel_infer": parallel_infer,
        "repetition_penalty": float(repetition_penalty),
        "sample_steps": int(sample_steps),
        "super_sampling": super_sampling,
    }
    return await tts_handle(req)

@APP.post("/tts")
async def tts_post_endpoint(request: TTS_Request):
    req = request.dict()
    return await tts_handle(req)

@APP.get("/set_refer_audio")
async def set_refer_aduio(refer_audio_path: str = None):
    try:
        tts_pipeline.set_ref_audio(refer_audio_path)
    except Exception as e:
        return JSONResponse(status_code=400, content={"message": "set refer audio failed", "Exception": str(e)})
    return JSONResponse(status_code=200, content={"message": "success"})

@APP.get("/set_gpt_weights")
async def set_gpt_weights(weights_path: str = None):
    try:
        if weights_path in ["", None]:
            return JSONResponse(status_code=400, content={"message": "gpt weight path is required"})
        tts_pipeline.init_t2s_weights(weights_path)
    except Exception as e:
        return JSONResponse(status_code=400, content={"message": "change gpt weight failed", "Exception": str(e)})

    return JSONResponse(status_code=200, content={"message": "success"})

@APP.get("/set_sovits_weights")
async def set_sovits_weights(weights_path: str = None):
    try:
        if weights_path in ["", None]:
            return JSONResponse(status_code=400, content={"message": "sovits weight path is required"})
        tts_pipeline.init_vits_weights(weights_path)
    except Exception as e:
        return JSONResponse(status_code=400, content={"message": "change sovits weight failed", "Exception": str(e)})
    return JSONResponse(status_code=200, content={"message": "success"})

@APP.get("/set_version")
async def set_version(ver: str = None):
    try:
        if ver in [None, ""]:
            return JSONResponse(status_code=400, content={"message": "ver is required (v1|v2|v3|v4|v2Pro|v2ProPlus)"})
        # 允许的版本
        allow = ["v1", "v2", "v3", "v4", "v2Pro", "v2ProPlus"]
        if ver not in allow:
            return JSONResponse(status_code=400, content={"message": f"ver '{ver}' not in {allow}"})

        # 从TTS_Config默认映射中取权重路径
        try:
            default_map = TTS_Config.default_configs.get(ver)
        except Exception:
            default_map = None
        if not default_map:
            return JSONResponse(status_code=400, content={"message": f"no default mapping for {ver}"})

        t2s_path = default_map.get("t2s_weights_path")
        vits_path = default_map.get("vits_weights_path")
        if not t2s_path or not vits_path:
            return JSONResponse(status_code=400, content={"message": f"mapping for {ver} missing paths"})

        # 切换权重
        tts_pipeline.init_t2s_weights(t2s_path)
        tts_pipeline.init_vits_weights(vits_path)
        # 更新内存中的版本标记（非必须）
        try:
            tts_config.update_version(ver)
        except Exception:
            pass
    except Exception as e:
        return JSONResponse(status_code=400, content={"message": "set version failed", "Exception": str(e)})
    return JSONResponse(status_code=200, content={"message": "success", "version": ver})

@APP.get("/set_emotion_mapping")
async def set_emotion_mapping(
    emotion: str = None,
    ref_audio_path: str = None,
    prompt_text: str = None,
    prompt_lang: str = None
):
    try:
        if emotion in ["", None]:
            return JSONResponse(status_code=400, content={"message": "emotion is required"})
        if ref_audio_path in ["", None]:
            return JSONResponse(status_code=400, content={"message": "ref_audio_path is required"})
        if prompt_text in ["", None]:
            return JSONResponse(status_code=400, content={"message": "prompt_text is required"})
        if prompt_lang in ["", None]:
            return JSONResponse(status_code=400, content={"message": "prompt_lang is required"})
        
        success = emotion_config.set_emotion_mapping(emotion, ref_audio_path, prompt_text, prompt_lang)
        if not success:
            return JSONResponse(status_code=400, content={"message": "Failed to save emotion mapping"})
    except Exception as e:
        return JSONResponse(status_code=400, content={"message": "set emotion mapping failed", "Exception": str(e)})
    
    return JSONResponse(status_code=200, content={"message": "success"})

@APP.get("/get_emotion_mapping")
async def get_emotion_mapping():
    try:
        return JSONResponse(status_code=200, content=emotion_config.emotion_mapping)
    except Exception as e:
        return JSONResponse(status_code=400, content={"message": "get emotion mapping failed", "Exception": str(e)})

@APP.get("/get_emotion_cache_stats")
async def get_emotion_cache_stats(emotion: str = None):
    try:
        if emotion in [None, ""]:
            return JSONResponse(status_code=200, content=emotion_cache_stats)
        return JSONResponse(status_code=200, content={emotion: emotion_cache_stats.get(emotion, {})})
    except Exception as e:
        return JSONResponse(status_code=400, content={"message": "get emotion cache stats failed", "Exception": str(e)})

def _prewarm_emotion_internal(emotion: str, force: bool = False) -> dict:
    mapping = emotion_config.get_emotion_mapping(emotion)
    if not mapping:
        return {"emotion": emotion, "status": "not_found"}
    ref_audio_path = mapping.get("ref_audio_path", "")
    prompt_text = mapping.get("prompt_text", "")
    prompt_lang = mapping.get("prompt_lang", "")
    if not (ref_audio_path and prompt_text and prompt_lang):
        return {"emotion": emotion, "status": "invalid_mapping"}
    if (not force) and (emotion in emotion_prompt_cache):
        # 若已存在且未变更，视为hit
        cached = emotion_prompt_cache[emotion]
        if (
            cached.get("ref_audio_path") == ref_audio_path
            and cached.get("prompt_text") == prompt_text
            and cached.get("prompt_lang") == prompt_lang
        ):
            emotion_cache_stats[emotion] = {
                **emotion_cache_stats.get(emotion, {}),
                "status": "hit",
            }
            return {"emotion": emotion, "status": "hit"}
    # 执行（或刷新）预热
    prepare_emotion_cache(emotion, ref_audio_path, prompt_text, prompt_lang)
    return {"emotion": emotion, **emotion_cache_stats.get(emotion, {}), "status": emotion_cache_stats.get(emotion, {}).get("status", "miss")}

@APP.get("/prewarm_emotion")
async def prewarm_emotion(emotion: str = None, force: bool = False):
    if emotion in [None, ""]:
        return JSONResponse(status_code=400, content={"message": "emotion is required"})
    try:
        res = _prewarm_emotion_internal(emotion, force)
        return JSONResponse(status_code=200, content=res)
    except Exception as e:
        return JSONResponse(status_code=400, content={"message": "prewarm emotion failed", "Exception": str(e)})

@APP.get("/prewarm_all")
async def prewarm_all(force: bool = False):
    try:
        results = []
        for emo in list(emotion_config.emotion_mapping.keys()):
            results.append(_prewarm_emotion_internal(emo, force))
        return JSONResponse(status_code=200, content={"results": results})
    except Exception as e:
        return JSONResponse(status_code=400, content={"message": "prewarm all failed", "Exception": str(e)})

def _auto_prewarm_worker():
    try:
        for emo in list(emotion_config.emotion_mapping.keys()):
            try:
                _prewarm_emotion_internal(emo, force=False)
            except Exception:
                pass
    except Exception:
        pass

@APP.on_event("startup")
async def on_startup():
    if auto_prewarm and emotion_config.emotion_mapping:
        th = threading.Thread(target=_auto_prewarm_worker, daemon=True)
        th.start()

if __name__ == "__main__":
    try:
        if host == "None":  # 在调用时使用 -a None 参数，可以让api监听双栈
            host = None
        uvicorn.run(app=APP, host=host, port=port, workers=1)
    except Exception:
        traceback.print_exc()
        os.kill(os.getpid(), signal.SIGTERM)
        exit(0)
