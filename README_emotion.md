# GPT-SoVITS 情感标签版API

这是GPT-SoVITS的情感标签版API，可以根据句子的情绪标签自动匹配对应的参考音频，实现情感切换功能。

## 安装

确保已经安装了原版GPT-SoVITS的所有依赖。

## 使用方法

### 启动API服务

```bash
python api_emotion.py -a 127.0.0.1 -p 9880 -c GPT_SoVITS/configs/tts_infer.yaml
```

参数说明:
- `-a` - 绑定地址，默认"127.0.0.1"
- `-p` - 绑定端口，默认9880
- `-c` - TTS配置文件路径，默认"GPT_SoVITS/configs/tts_infer.yaml"
- `-e` - 情感音频映射配置文件路径，默认"emotion_config.json"

### 设置情感音频映射

在使用前，需要先设置情感标签与参考音频的映射关系。有两种方式：

#### 1. 通过API设置

```
GET http://127.0.0.1:9880/set_emotion_mapping?emotion=开心&ref_audio_path=happy.wav&prompt_text=我很开心&prompt_lang=zh
```

#### 2. 直接编辑配置文件

编辑 `emotion_config.json` 文件：

```json
{
    "开心": {
        "ref_audio_path": "happy.wav",
        "prompt_text": "我今天非常开心",
        "prompt_lang": "zh"
    },
    "生气": {
        "ref_audio_path": "angry.wav",
        "prompt_text": "我现在很生气",
        "prompt_lang": "zh"
    },
    "难过": {
        "ref_audio_path": "sad.wav",
        "prompt_text": "我感到很难过",
        "prompt_lang": "zh"
    }
}
```

### 使用情感标签进行推理

在文本前面加上`[情感标签]`，API会自动匹配对应的参考音频：

```
GET http://127.0.0.1:9880/tts?text=[开心]今天能吃红烧肉了！&text_lang=zh&prompt_lang=zh
```

或者使用POST请求：

```json
{
    "text": "[生气]红烧肉居然卖50一盘？",
    "text_lang": "zh",
    "prompt_lang": "zh"
}
```

API会自动：
1. 提取情感标签"开心"或"生气"
2. 查找对应的参考音频、提示文本和语言
3. 使用这些参数进行语音合成
4. 返回合成的音频

## API接口

### 推理接口

- **GET/POST** `/tts`
- 参数：与原版api_v2.py相同，但text参数可以包含情感标签

### 情感映射管理接口

- **GET** `/set_emotion_mapping` - 设置情感映射
- **GET** `/get_emotion_mapping` - 获取所有情感映射

### 其他接口

- **GET** `/control` - 控制命令（重启/退出）
- **GET** `/set_gpt_weights` - 切换GPT模型
- **GET** `/set_sovits_weights` - 切换Sovits模型
- **GET** `/set_refer_audio` - 设置参考音频

## 示例

### 多种情感的文本合成

```
[开心]今天能吃红烧肉了！
[生气]红烧肉居然卖50一盘？
[难过]吃这么胖怎么找女朋友……
```

每句话会根据情感标签自动切换不同的参考音频，实现情感变化。

## 注意事项

1. 确保情感标签已在配置中设置，否则API将返回错误
2. 情感标签格式为`[标签]`，放在句子开头
3. 可以根据需要自定义更多情感标签
