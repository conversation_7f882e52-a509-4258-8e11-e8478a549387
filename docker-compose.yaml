version: "3.8"

services:
  GPT-SoVITS-CU126:
    image: xxxxrt666/gpt-sovits:latest-cu126
    container_name: GPT-SoVITS-CU126
    ports:
      - "9871:9871"
      - "9872:9872"
      - "9873:9873"
      - "9874:9874"
      - "9880:9880"
    volumes:
      - .:/workspace/GPT-SoVITS
    environment:
      - is_half=true
    tty: true
    stdin_open: true
    shm_size: "16g"
    restart: unless-stopped
    runtime: nvidia
  GPT-SoVITS-CU126-Lite:
    image: xxxxrt666/gpt-sovits:latest-cu126-lite
    container_name: GPT-SoVITS-CU126-Lite
    ports:
      - "9871:9871"
      - "9872:9872"
      - "9873:9873"
      - "9874:9874"
      - "9880:9880"
    volumes:
      - .:/workspace/GPT-SoVITS
      - tools/asr/models:/workspace/models/asr_models
      - tools/uvr5/uvr5_weights:/workspace/models/uvr5_weights
    environment:
      - is_half=true
    tty: true
    stdin_open: true
    shm_size: "16g"
    restart: unless-stopped
    runtime: nvidia
  GPT-SoVITS-CU128:
    image: xxxxrt666/gpt-sovits:latest-cu128
    container_name: GPT-SoVITS-CU128
    ports:
      - "9871:9871"
      - "9872:9872"
      - "9873:9873"
      - "9874:9874"
      - "9880:9880"
    volumes:
      - .:/workspace/GPT-SoVITS
    environment:
      - is_half=true
    tty: true
    stdin_open: true
    shm_size: "16g"
    restart: unless-stopped
    runtime: nvidia
  GPT-SoVITS-CU128-Lite:
    image: xxxxrt666/gpt-sovits:latest-cu128-lite
    container_name: GPT-SoVITS-CU128-Lite
    ports:
      - "9871:9871"
      - "9872:9872"
      - "9873:9873"
      - "9874:9874"
      - "9880:9880"
    volumes:
      - .:/workspace/GPT-SoVITS
      - tools/asr/models:/workspace/models/asr_models
      - tools/uvr5/uvr5_weights:/workspace/models/uvr5_weights
    environment:
      - is_half=true
    tty: true
    stdin_open: true
    shm_size: "16g"
    restart: unless-stopped
    runtime: nvidia